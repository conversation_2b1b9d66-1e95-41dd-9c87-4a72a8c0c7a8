// Test improved answer text for Add22Thc questionnaire
// This verifies that answerText contains meaningful content instead of "Selected/Not selected"

console.log("=== Improved Answer Text Test ===\n");

// Scoring mapping object
const scoringMap = {
  reasonSideEffects: { true: 4, false: 0 },
  reasonGentlerEffect: { true: 3, false: 0 },
  reasonDifferentStrain: { true: 2, false: 0 },
  reasonTolerance: { true: 3, false: 0 },
  reasonOther: { true: 1, false: 0 },
  symptomImprovement: {
    "1": 0, "2": 0, "3": 1, "4": 1, "5": 2,
    "6": 2, "7": 3, "8": 3, "9": 4, "10": 4
  },
  sideEffectsNone: { true: 4, false: 0 },
  sideEffectsMild: { true: 2, false: 0 },
  sideEffectsModerate: { true: 1, false: 0 },
  sideEffectsStrong: { true: 0, false: 0 },
  healthChanges: { "no-changes": 3, "yes": 1 },
  usagePlan: {
    "alternative-situations": 4,
    "rotation-time-symptoms": 3,
    "unsure-advice": 2,
    "other": 1
  },
  consent: { "yes": 5, "no": 0 }
};

function calculateQuestionScore(questionKey, answer) {
  const questionScoring = scoringMap[questionKey];
  if (!questionScoring) return 0;
  const answerKey = typeof answer === 'boolean' ? answer.toString() : answer;
  return questionScoring[answerKey] || 0;
}

function getSymptomImprovementText(value) {
  const num = parseInt(value);
  if (num === 10) return "Complete relief";
  if (num >= 9) return "Very effective";
  if (num >= 7) return "Good improvement";
  if (num >= 5) return "Some improvement";
  if (num >= 3) return "Minimal improvement";
  return "No improvement";
}

// Simulate the improved questionsAndAnswers creation
function createImprovedQuestionsAndAnswers(formData) {
  const questionsAndAnswers = [];

  // Question 1: Individual reason checkboxes with meaningful text
  questionsAndAnswers.push({
    questionKey: "reasonSideEffects",
    questionText: "Reason: Experiencing side effects with 29%",
    answerValue: formData.reasonSideEffects,
    answerText: formData.reasonSideEffects ? "Experiencing side effects with 29% (e.g., anxiety, dizziness, increased heart rate)" : "Not experiencing side effects with 29%",
    score: calculateQuestionScore('reasonSideEffects', formData.reasonSideEffects)
  });

  questionsAndAnswers.push({
    questionKey: "reasonGentlerEffect",
    questionText: "Reason: Prefer a gentler effect for daily use",
    answerValue: formData.reasonGentlerEffect,
    answerText: formData.reasonGentlerEffect ? "Prefer a gentler effect for daily or daytime use" : "Do not prefer gentler effect",
    score: calculateQuestionScore('reasonGentlerEffect', formData.reasonGentlerEffect)
  });

  questionsAndAnswers.push({
    questionKey: "reasonDifferentStrain",
    questionText: "Reason: Trying a different strain for symptom targeting",
    answerValue: formData.reasonDifferentStrain,
    answerText: formData.reasonDifferentStrain ? "Trying a different strain for symptom targeting (e.g., sleep, focus, mood)" : "Not interested in different strain",
    score: calculateQuestionScore('reasonDifferentStrain', formData.reasonDifferentStrain)
  });

  questionsAndAnswers.push({
    questionKey: "reasonTolerance",
    questionText: "Reason: Building tolerance to higher THC strain",
    answerValue: formData.reasonTolerance,
    answerText: formData.reasonTolerance ? "Building tolerance to higher THC strain" : "Not building tolerance",
    score: calculateQuestionScore('reasonTolerance', formData.reasonTolerance)
  });

  questionsAndAnswers.push({
    questionKey: "reasonOther",
    questionText: "Reason: Other",
    answerValue: formData.reasonOther,
    answerText: formData.reasonOther ? `Other reason: ${formData.reasonOtherText || 'No description provided'}` : "No other reason",
    score: calculateQuestionScore('reasonOther', formData.reasonOther)
  });

  // Question 2: Symptom improvement
  questionsAndAnswers.push({
    questionKey: "symptomImprovement",
    questionText: "How well has your current 29% THC treatment worked for you?",
    answerValue: formData.symptomImprovement,
    answerText: `${formData.symptomImprovement} - ${getSymptomImprovementText(formData.symptomImprovement)}`,
    score: calculateQuestionScore('symptomImprovement', formData.symptomImprovement)
  });

  // Question 3: Individual side effect checkboxes with meaningful text
  questionsAndAnswers.push({
    questionKey: "sideEffectsNone",
    questionText: "Side effects: None",
    answerValue: formData.sideEffectsNone,
    answerText: formData.sideEffectsNone ? "None" : "Experiencing some side effects",
    score: calculateQuestionScore('sideEffectsNone', formData.sideEffectsNone)
  });

  questionsAndAnswers.push({
    questionKey: "sideEffectsMild",
    questionText: "Side effects: Mild",
    answerValue: formData.sideEffectsMild,
    answerText: formData.sideEffectsMild ? "Mild (e.g., dry mouth, mild sedation, tiredness)" : "No mild side effects",
    score: calculateQuestionScore('sideEffectsMild', formData.sideEffectsMild)
  });

  questionsAndAnswers.push({
    questionKey: "sideEffectsModerate",
    questionText: "Side effects: Moderate",
    answerValue: formData.sideEffectsModerate,
    answerText: formData.sideEffectsModerate ? "Moderate (e.g., dizziness, nausea, appetite changes)" : "No moderate side effects",
    score: calculateQuestionScore('sideEffectsModerate', formData.sideEffectsModerate)
  });

  questionsAndAnswers.push({
    questionKey: "sideEffectsStrong",
    questionText: "Side effects: Strong",
    answerValue: formData.sideEffectsStrong,
    answerText: formData.sideEffectsStrong ? "Strong (e.g., anxiety, mood changes, racing heart, confusion)" : "No strong side effects",
    score: calculateQuestionScore('sideEffectsStrong', formData.sideEffectsStrong)
  });

  // Question 4: Health changes
  questionsAndAnswers.push({
    questionKey: "healthChanges",
    questionText: "Have there been any changes in your health, medications, or lifestyle since your last consultation?",
    answerValue: formData.healthChanges,
    answerText: formData.healthChanges === 'yes' ? 'Yes, there have been changes' : 'No changes',
    score: calculateQuestionScore('healthChanges', formData.healthChanges)
  });

  // Question 5: Usage plan
  questionsAndAnswers.push({
    questionKey: "usagePlan",
    questionText: "How do you plan to use the 22% THC product alongside your 29%?",
    answerValue: formData.usagePlan,
    answerText: formData.usagePlan === 'alternative-situations' ? 'As an alternative for specific times/situations' :
                formData.usagePlan === 'rotation-time-symptoms' ? 'In rotation with 29% depending on time of day or symptoms' :
                formData.usagePlan === 'unsure-advice' ? 'Unsure – would like advice from my doctor' :
                formData.usagePlan === 'other' ? 'Other' : formData.usagePlan,
    score: calculateQuestionScore('usagePlan', formData.usagePlan)
  });

  // Question 6: Consent
  questionsAndAnswers.push({
    questionKey: "consent",
    questionText: "Do you consent to your doctor reviewing this information?",
    answerValue: formData.consent,
    answerText: formData.consent === 'yes' ? 'Yes' : 'No',
    score: calculateQuestionScore('consent', formData.consent)
  });

  return questionsAndAnswers;
}

// Test case: Patient with mixed responses
const testFormData = {
  // Reasons - some selected
  reasonSideEffects: true,
  reasonGentlerEffect: true,
  reasonDifferentStrain: false,
  reasonTolerance: false,
  reasonOther: true,
  reasonOtherText: "Cost considerations and availability",

  // Symptom improvement
  symptomImprovement: "8",

  // Side effects - mild selected
  sideEffectsNone: false,
  sideEffectsMild: true,
  sideEffectsModerate: false,
  sideEffectsStrong: false,

  // Health changes
  healthChanges: "no-changes",

  // Usage plan
  usagePlan: "alternative-situations",

  // Consent
  consent: "yes"
};

console.log("🧪 Testing improved answer text...\n");

const questionsAndAnswers = createImprovedQuestionsAndAnswers(testFormData);

// Display results
console.log("=== Sample Questionnaire Submission ===");
questionsAndAnswers.forEach((qa, index) => {
  console.log(`${index + 1}. ${qa.questionKey}`);
  console.log(`   Question: ${qa.questionText}`);
  console.log(`   Answer Value: ${qa.answerValue}`);
  console.log(`   Answer Text: "${qa.answerText}"`);
  console.log(`   Score: ${qa.score} points`);
  console.log("");
});

// Verify no generic "Selected/Not selected" text
console.log("=== Answer Text Quality Check ===");
let hasGenericText = false;
let meaningfulAnswers = 0;

questionsAndAnswers.forEach(qa => {
  if (qa.answerText.includes("Selected") || qa.answerText.includes("Not selected")) {
    console.log(`❌ Generic text found: ${qa.questionKey} - "${qa.answerText}"`);
    hasGenericText = true;
  } else {
    console.log(`✅ Meaningful text: ${qa.questionKey} - "${qa.answerText}"`);
    meaningfulAnswers++;
  }
});

console.log(`\n=== Summary ===`);
console.log(`Total questions: ${questionsAndAnswers.length}`);
console.log(`Meaningful answers: ${meaningfulAnswers}`);
console.log(`Generic text found: ${hasGenericText ? 'YES ❌' : 'NO ✅'}`);

if (!hasGenericText) {
  console.log("\n🎉 SUCCESS: All answer text is meaningful and descriptive!");
  console.log("Doctors and analytics will now see actual patient responses instead of generic 'Selected' text.");
} else {
  console.log("\n⚠️  Some answers still use generic text. Review needed.");
}

console.log("\n=== Benefits for Doctors ===");
console.log("• Clear understanding of patient's specific reasons");
console.log("• Detailed side effect information");
console.log("• Specific symptom improvement ratings with descriptions");
console.log("• Exact usage preferences");
console.log("• Better clinical decision making");

console.log("\n=== Benefits for Analytics ===");
console.log("• Track most common reasons for 22% THC requests");
console.log("• Analyze side effect patterns");
console.log("• Monitor symptom improvement distributions");
console.log("• Understand patient preferences for usage plans");
console.log("• Identify trends in patient responses");
