# Backend API Specification for Dynamic Questionnaire Rendering

## Overview

This document specifies the exact API responses needed to dynamically render the existing questionnaire components (`Add22Thc.tsx`, `ThcIncrease.tsx`, `ExtendTP.tsx`, `QuantityIncrease.tsx`) without modifying their current UI structure.

## Core API Endpoints

### 1. Get Questionnaire Configuration
```
GET /api/questionnaires/{type}/config
```

**Parameters:**
- `type`: `'add-22-thc' | 'thc-increase' | 'extend-tp' | 'quantity-increase'`
- `patientId` (query): Required for questionnaires with dynamic options

**Response Structure:**
```typescript
interface QuestionnaireConfigResponse {
  id: string;
  type: string;
  name: string;
  version: string;
  isActive: boolean;
  maxScore: number;
  threshold: number;
  steps: StepConfig[];
  scoringMap: Record<string, Record<string, number>>;
  specialLogic?: SpecialLogicConfig[];
}

interface StepConfig {
  stepNumber: number;
  title: string;
  questions: QuestionConfig[];
}

interface QuestionConfig {
  key: string;
  type: 'radio' | 'checkbox' | 'slider' | 'text' | 'dropdown';
  label: string;
  subLabel?: string;
  isRequired: boolean;
  contributesToScore: boolean;
  options?: OptionConfig[];
  sliderConfig?: SliderConfig;
  textConfig?: TextConfig;
  conditionalOn?: ConditionalConfig;
}

interface OptionConfig {
  value: string;
  label: string;
  score?: number;
}

interface SliderConfig {
  min: number;
  max: number;
  step: number;
  marks: boolean;
  valueLabelDisplay: 'on' | 'off' | 'auto';
}

interface TextConfig {
  multiline: boolean;
  rows?: number;
  maxLength?: number;
  placeholder?: string;
}

interface ConditionalConfig {
  dependsOn: string; // question key
  showWhen: string[]; // values that trigger showing this question
}

interface SpecialLogicConfig {
  questionKey: string;
  logicType: 'condition' | 'sideEffect';
  description: string;
}
```

## Questionnaire-Specific API Responses

### Add 22% THC Questionnaire

**Endpoint:** `GET /api/questionnaires/add-22-thc/config`

```json
{
  "id": "add-22-thc-v1",
  "type": "add-22-thc",
  "name": "Add 22% THC Assessment",
  "version": "1.0",
  "isActive": true,
  "maxScore": 33,
  "threshold": 7,
  "steps": [
    {
      "stepNumber": 1,
      "title": "Reasons for Requesting 22% THC",
      "questions": [
        {
          "key": "reasonSideEffects",
          "type": "checkbox",
          "label": "Why do you want to add a 22% THC product to your treatment plan? (Select all that apply)",
          "isRequired": false,
          "contributesToScore": true,
          "options": [
            {"value": "true", "label": "Experiencing side effects with 29%", "score": 4},
            {"value": "false", "label": "", "score": 0}
          ]
        },
        {
          "key": "reasonGentlerEffect",
          "type": "checkbox",
          "label": "",
          "isRequired": false,
          "contributesToScore": true,
          "options": [
            {"value": "true", "label": "Prefer a gentler effect for daily or daytime use", "score": 3},
            {"value": "false", "label": "", "score": 0}
          ]
        },
        {
          "key": "reasonDifferentStrain",
          "type": "checkbox",
          "label": "",
          "isRequired": false,
          "contributesToScore": true,
          "options": [
            {"value": "true", "label": "Trying a different strain for symptom targeting", "score": 2},
            {"value": "false", "label": "", "score": 0}
          ]
        },
        {
          "key": "reasonTolerance",
          "type": "checkbox",
          "label": "",
          "isRequired": false,
          "contributesToScore": true,
          "options": [
            {"value": "true", "label": "Building tolerance to higher THC strain", "score": 3},
            {"value": "false", "label": "", "score": 0}
          ]
        },
        {
          "key": "reasonOther",
          "type": "checkbox",
          "label": "",
          "isRequired": false,
          "contributesToScore": true,
          "options": [
            {"value": "true", "label": "Other (please describe):", "score": 1},
            {"value": "false", "label": "", "score": 0}
          ]
        },
        {
          "key": "reasonOtherText",
          "type": "text",
          "label": "",
          "isRequired": false,
          "contributesToScore": false,
          "textConfig": {
            "multiline": true,
            "rows": 2,
            "placeholder": "Please describe your other reason..."
          },
          "conditionalOn": {
            "dependsOn": "reasonOther",
            "showWhen": ["true"]
          }
        }
      ]
    },
    {
      "stepNumber": 2,
      "title": "Current Response to 29% THC",
      "questions": [
        {
          "key": "symptomImprovement",
          "type": "slider",
          "label": "How well has your current 29% THC treatment worked for you?",
          "subLabel": "Rate your symptom improvement",
          "isRequired": true,
          "contributesToScore": true,
          "sliderConfig": {
            "min": 1,
            "max": 10,
            "step": 1,
            "marks": true,
            "valueLabelDisplay": "on"
          }
        },
        {
          "key": "sideEffectsNone",
          "type": "checkbox",
          "label": "Have you experienced any side effects with the 29% THC product? (Select all that apply)",
          "isRequired": false,
          "contributesToScore": true,
          "options": [
            {"value": "true", "label": "None", "score": 4},
            {"value": "false", "label": "", "score": 0}
          ]
        },
        {
          "key": "sideEffectsMild",
          "type": "checkbox",
          "label": "",
          "isRequired": false,
          "contributesToScore": true,
          "options": [
            {"value": "true", "label": "Mild (e.g., dry mouth, mild sedation, tiredness)", "score": 2},
            {"value": "false", "label": "", "score": 0}
          ]
        },
        {
          "key": "sideEffectsModerate",
          "type": "checkbox",
          "label": "",
          "isRequired": false,
          "contributesToScore": true,
          "options": [
            {"value": "true", "label": "Moderate (e.g., dizziness, nausea, appetite changes)", "score": 1},
            {"value": "false", "label": "", "score": 0}
          ]
        },
        {
          "key": "sideEffectsStrong",
          "type": "checkbox",
          "label": "",
          "isRequired": false,
          "contributesToScore": true,
          "options": [
            {"value": "true", "label": "Strong (e.g., anxiety, mood changes, racing heart, confusion)", "score": 0},
            {"value": "false", "label": "", "score": 0}
          ]
        },
        {
          "key": "sideEffectsDescription",
          "type": "text",
          "label": "",
          "isRequired": false,
          "contributesToScore": false,
          "textConfig": {
            "multiline": true,
            "rows": 3,
            "placeholder": "Please describe your side effects..."
          },
          "conditionalOn": {
            "dependsOn": "sideEffectsModerate,sideEffectsStrong",
            "showWhen": ["true"]
          }
        }
      ]
    },
    {
      "stepNumber": 3,
      "title": "Health Changes Assessment",
      "questions": [
        {
          "key": "healthChanges",
          "type": "radio",
          "label": "Have there been any changes in your health, medications, or lifestyle since your last consultation?",
          "isRequired": true,
          "contributesToScore": true,
          "options": [
            {"value": "no-changes", "label": "No changes", "score": 3},
            {"value": "yes", "label": "Yes — please describe:", "score": 1}
          ]
        },
        {
          "key": "healthChangesDescription",
          "type": "text",
          "label": "",
          "isRequired": false,
          "contributesToScore": false,
          "textConfig": {
            "multiline": true,
            "rows": 3,
            "placeholder": "Please describe the changes..."
          },
          "conditionalOn": {
            "dependsOn": "healthChanges",
            "showWhen": ["yes"]
          }
        }
      ]
    },
    {
      "stepNumber": 4,
      "title": "Expectations and Usage Planning",
      "questions": [
        {
          "key": "expectations",
          "type": "text",
          "label": "What do you hope to achieve by adding a 22% THC product?",
          "isRequired": false,
          "contributesToScore": false,
          "textConfig": {
            "multiline": true,
            "rows": 3,
            "placeholder": "Describe your expectations and goals..."
          }
        },
        {
          "key": "concerns",
          "type": "text",
          "label": "Do you have any concerns about adding this option?",
          "isRequired": false,
          "contributesToScore": false,
          "textConfig": {
            "multiline": true,
            "rows": 3,
            "placeholder": "Describe any concerns you may have..."
          }
        },
        {
          "key": "usagePlan",
          "type": "radio",
          "label": "How do you plan to use the 22% THC product alongside your 29%?",
          "isRequired": true,
          "contributesToScore": true,
          "options": [
            {"value": "alternative-situations", "label": "As an alternative for specific times/situations", "score": 4},
            {"value": "rotation-time-symptoms", "label": "In rotation with 29% depending on time of day or symptoms", "score": 3},
            {"value": "unsure-advice", "label": "Unsure – would like advice from my doctor", "score": 2},
            {"value": "other", "label": "Other (please describe):", "score": 1}
          ]
        }
      ]
    },
    {
      "stepNumber": 5,
      "title": "Consent",
      "questions": [
        {
          "key": "consent",
          "type": "radio",
          "label": "Do you consent to your doctor reviewing this information, accessing your MyHealth Record if needed, and updating your treatment plan if appropriate?",
          "isRequired": true,
          "contributesToScore": true,
          "options": [
            {"value": "yes", "label": "Yes", "score": 5},
            {"value": "no", "label": "No", "score": 0}
          ]
        }
      ]
    }
  ],
  "scoringMap": {
    "reasonSideEffects": {"true": 4, "false": 0},
    "reasonGentlerEffect": {"true": 3, "false": 0},
    "reasonDifferentStrain": {"true": 2, "false": 0},
    "reasonTolerance": {"true": 3, "false": 0},
    "reasonOther": {"true": 1, "false": 0},
    "symptomImprovement": {"1": 0, "2": 0, "3": 1, "4": 1, "5": 2, "6": 2, "7": 3, "8": 3, "9": 4, "10": 4},
    "sideEffectsNone": {"true": 4, "false": 0},
    "sideEffectsMild": {"true": 2, "false": 0},
    "sideEffectsModerate": {"true": 1, "false": 0},
    "sideEffectsStrong": {"true": 0, "false": 0},
    "healthChanges": {"no-changes": 3, "yes": 1},
    "usagePlan": {"alternative-situations": 4, "rotation-time-symptoms": 3, "unsure-advice": 2, "other": 1},
    "consent": {"yes": 5, "no": 0}
  }
}
```

### THC Increase Questionnaire

**Endpoint:** `GET /api/questionnaires/thc-increase/config`

```json
{
  "id": "thc-increase-v1",
  "type": "thc-increase",
  "name": "THC Increase Assessment",
  "version": "1.0",
  "isActive": true,
  "maxScore": 61,
  "threshold": 45,
  "steps": [
    {
      "stepNumber": 1,
      "title": "Current Usage Patterns",
      "questions": [
        {
          "key": "consistency",
          "type": "radio",
          "label": "How consistent were you in using 22% THC flower during the two-week trial?",
          "isRequired": true,
          "contributesToScore": true,
          "options": [
            {"value": "every-day", "label": "Every day as prescribed", "score": 3},
            {"value": "most-days", "label": "Most days", "score": 2},
            {"value": "occasionally", "label": "Occasionally", "score": 1},
            {"value": "rarely", "label": "Rarely", "score": 0}
          ]
        },
        {
          "key": "dosage",
          "type": "radio",
          "label": "What dosage of 22% THC flower were you taking during the trial?",
          "isRequired": true,
          "contributesToScore": true,
          "options": [
            {"value": "less-than-0-5g", "label": "Less than 0.5g/day", "score": 1},
            {"value": "0-5g-1g", "label": "0.5g - 1g/day", "score": 2},
            {"value": "1g-2g", "label": "1g - 2g/day", "score": 3},
            {"value": "more-than-2g", "label": "More than 2g/day", "score": 4}
          ]
        },
        {
          "key": "frequency",
          "type": "radio",
          "label": "How often did you use 22% THC flower?",
          "isRequired": true,
          "contributesToScore": true,
          "options": [
            {"value": "once-a-day", "label": "Once a day", "score": 1},
            {"value": "twice-a-day", "label": "Twice a day", "score": 2},
            {"value": "three-times-a-day", "label": "Three times a day", "score": 3},
            {"value": "as-needed", "label": "As needed", "score": 1}
          ]
        }
      ]
    },
    {
      "stepNumber": 2,
      "title": "Treatment Assessment",
      "questions": [
        {
          "key": "condition",
          "type": "radio",
          "label": "What condition are you using THC flower to treat?",
          "isRequired": true,
          "contributesToScore": true,
          "options": [
            {"value": "chronic-pain", "label": "Chronic pain", "score": 2},
            {"value": "anxiety", "label": "Anxiety", "score": 2},
            {"value": "insomnia", "label": "Insomnia", "score": 2},
            {"value": "inflammation", "label": "Inflammation", "score": 2},
            {"value": "other", "label": "Other (Please specify):", "score": 0}
          ]
        },
        {
          "key": "conditionOther",
          "type": "text",
          "label": "",
          "isRequired": false,
          "contributesToScore": false,
          "textConfig": {
            "multiline": false,
            "maxLength": 200,
            "placeholder": "Please specify your condition..."
          },
          "conditionalOn": {
            "dependsOn": "condition",
            "showWhen": ["other"]
          }
        },
        {
          "key": "effectiveness",
          "type": "radio",
          "label": "On a scale of 1 to 10, how effective has 22% THC flower been in managing your symptoms?",
          "isRequired": true,
          "contributesToScore": true,
          "options": [
            {"value": "1-2", "label": "1-2", "score": 0},
            {"value": "3-4", "label": "3-4", "score": 1},
            {"value": "5-6", "label": "5-6", "score": 2},
            {"value": "7-8", "label": "7-8", "score": 3},
            {"value": "9-10", "label": "9-10", "score": 4}
          ]
        }
      ]
    }
  ],
  "scoringMap": {
    "consistency": {"every-day": 3, "most-days": 2, "occasionally": 1, "rarely": 0},
    "dosage": {"less-than-0-5g": 1, "0-5g-1g": 2, "1g-2g": 3, "more-than-2g": 4},
    "frequency": {"once-a-day": 1, "twice-a-day": 2, "three-times-a-day": 3, "as-needed": 1},
    "effectiveness": {"1-2": 0, "3-4": 1, "5-6": 2, "7-8": 3, "9-10": 4}
  },
  "specialLogic": [
    {
      "questionKey": "condition",
      "logicType": "condition",
      "description": "Any condition except 'other' gets 2 points, 'other' gets 0 points"
    }
  ]
}
```

### ExtendTP Questionnaire

**Endpoint:** `GET /api/questionnaires/extend-tp/config`

```json
{
  "id": "extend-tp-v1",
  "type": "extend-tp",
  "name": "Treatment Plan Extension Assessment",
  "version": "1.0",
  "isActive": true,
  "maxScore": 60,
  "threshold": 42,
  "steps": [
    {
      "stepNumber": 1,
      "title": "Treatment Adherence",
      "questions": [
        {
          "key": "adherence",
          "type": "radio",
          "label": "During the past 3 months, how often did you use your medicinal cannabis exactly as prescribed (correct dose and timing)?",
          "isRequired": true,
          "contributesToScore": true,
          "options": [
            {"value": "always-followed", "label": "Always followed the prescribed schedule", "score": 5},
            {"value": "usually-followed", "label": "Usually followed the schedule, with only a few minor misses or adjustments", "score": 4},
            {"value": "sometimes-followed", "label": "Sometimes followed the schedule, with occasional missed or extra doses", "score": 2},
            {"value": "rarely-followed", "label": "Rarely followed the prescribed schedule", "score": 1},
            {"value": "never-followed", "label": "Never followed the prescribed regimen", "score": 0}
          ]
        }
      ]
    },
    {
      "stepNumber": 2,
      "title": "Symptom Improvement",
      "questions": [
        {
          "key": "symptomImprovement",
          "type": "radio",
          "label": "How much has your primary symptom (the main condition you are treating, e.g. pain, anxiety, insomnia) improved since starting medicinal cannabis?",
          "isRequired": true,
          "contributesToScore": true,
          "options": [
            {"value": "significant-improvement", "label": "Significant improvement: symptoms have greatly reduced", "score": 5},
            {"value": "moderate-improvement", "label": "Moderate improvement: noticeable improvement, but symptoms are still present", "score": 4},
            {"value": "slight-improvement", "label": "Slight improvement: only a small improvement in symptoms", "score": 2},
            {"value": "no-improvement", "label": "No improvement: no change in the symptoms", "score": 1},
            {"value": "symptoms-worsened", "label": "Symptoms worsened: symptoms have become worse than before", "score": 0}
          ]
        }
      ]
    }
  ],
  "scoringMap": {
    "adherence": {"always-followed": 5, "usually-followed": 4, "sometimes-followed": 2, "rarely-followed": 1, "never-followed": 0},
    "symptomImprovement": {"significant-improvement": 5, "moderate-improvement": 4, "slight-improvement": 2, "no-improvement": 1, "symptoms-worsened": 0}
  }
}
```

### Quantity Increase Questionnaire

**Endpoint:** `GET /api/questionnaires/quantity-increase/config?patientId={id}`

```json
{
  "id": "quantity-increase-v1",
  "type": "quantity-increase",
  "name": "Quantity Increase Assessment",
  "version": "1.0",
  "isActive": true,
  "maxScore": 50,
  "threshold": 35,
  "steps": [
    {
      "stepNumber": 1,
      "title": "Strength Selection and Reasons",
      "questions": [
        {
          "key": "thc22Selected",
          "type": "checkbox",
          "label": "Which strength(s) would you like to increase? (Select all that apply)",
          "isRequired": false,
          "contributesToScore": false,
          "options": [
            {"value": "true", "label": "22% THC", "score": 0},
            {"value": "false", "label": "", "score": 0}
          ]
        },
        {
          "key": "thc29Selected",
          "type": "checkbox",
          "label": "",
          "isRequired": false,
          "contributesToScore": false,
          "options": [
            {"value": "true", "label": "29% THC", "score": 0},
            {"value": "false", "label": "", "score": 0}
          ]
        },
        {
          "key": "thc22RequestedQuantity",
          "type": "dropdown",
          "label": "Select new quantity for 22% THC:",
          "isRequired": false,
          "contributesToScore": false,
          "options": [
            {"value": "15", "label": "15g", "score": 0},
            {"value": "20", "label": "20g", "score": 0},
            {"value": "25", "label": "25g", "score": 0},
            {"value": "30", "label": "30g", "score": 0}
          ],
          "conditionalOn": {
            "dependsOn": "thc22Selected",
            "showWhen": ["true"]
          }
        },
        {
          "key": "thc29RequestedQuantity",
          "type": "dropdown",
          "label": "Select new quantity for 29% THC:",
          "isRequired": false,
          "contributesToScore": false,
          "options": [
            {"value": "20", "label": "20g", "score": 0},
            {"value": "25", "label": "25g", "score": 0},
            {"value": "30", "label": "30g", "score": 0}
          ],
          "conditionalOn": {
            "dependsOn": "thc29Selected",
            "showWhen": ["true"]
          }
        },
        {
          "key": "reasonNotLasting",
          "type": "checkbox",
          "label": "Why are you requesting an increase in the amount of medicinal cannabis approved under your current treatment plan? (Select all that apply)",
          "isRequired": false,
          "contributesToScore": true,
          "options": [
            {"value": "true", "label": "The current quantity is not lasting the full month", "score": 2},
            {"value": "false", "label": "", "score": 0}
          ]
        },
        {
          "key": "reasonHigherDoses",
          "type": "checkbox",
          "label": "",
          "isRequired": false,
          "contributesToScore": true,
          "options": [
            {"value": "true", "label": "I need more frequent or higher doses to manage symptoms", "score": 2},
            {"value": "false", "label": "", "score": 0}
          ]
        },
        {
          "key": "reasonTolerance",
          "type": "checkbox",
          "label": "",
          "isRequired": false,
          "contributesToScore": true,
          "options": [
            {"value": "true", "label": "I've developed tolerance and need more to achieve the same benefit", "score": 2},
            {"value": "false", "label": "", "score": 0}
          ]
        },
        {
          "key": "reasonIncreasedSymptoms",
          "type": "checkbox",
          "label": "",
          "isRequired": false,
          "contributesToScore": true,
          "options": [
            {"value": "true", "label": "My symptoms have increased (e.g. flare-ups, stress, pain)", "score": 2},
            {"value": "false", "label": "", "score": 0}
          ]
        },
        {
          "key": "reasonOther",
          "type": "checkbox",
          "label": "",
          "isRequired": false,
          "contributesToScore": true,
          "options": [
            {"value": "true", "label": "Other (please describe):", "score": 0},
            {"value": "false", "label": "", "score": 0}
          ]
        },
        {
          "key": "reasonOtherText",
          "type": "text",
          "label": "",
          "isRequired": false,
          "contributesToScore": false,
          "textConfig": {
            "multiline": true,
            "rows": 2,
            "placeholder": "Please describe your other reason..."
          },
          "conditionalOn": {
            "dependsOn": "reasonOther",
            "showWhen": ["true"]
          }
        }
      ]
    },
    {
      "stepNumber": 2,
      "title": "Current Treatment Response",
      "questions": [
        {
          "key": "currentEffectiveness",
          "type": "slider",
          "label": "On a scale of 1 to 10, how well has your current quantity helped manage your symptoms?",
          "subLabel": "Rate your symptom improvement (1 = no benefit, 10 = complete relief, but still requiring more quantity):",
          "isRequired": true,
          "contributesToScore": true,
          "sliderConfig": {
            "min": 1,
            "max": 10,
            "step": 1,
            "marks": true,
            "valueLabelDisplay": "on"
          }
        }
      ]
    }
  ],
  "scoringMap": {
    "reasonNotLasting": {"true": 2, "false": 0},
    "reasonHigherDoses": {"true": 2, "false": 0},
    "reasonTolerance": {"true": 2, "false": 0},
    "reasonIncreasedSymptoms": {"true": 2, "false": 0},
    "reasonOther": {"true": 0, "false": 0},
    "currentEffectiveness": {"1": 0, "2": 0, "3": 1, "4": 1, "5": 2, "6": 2, "7": 3, "8": 4, "9": 5, "10": 6}
  }
}
```

## Additional Required Endpoints

### 2. Submit Questionnaire Response
```
POST /api/questionnaires/{type}/submit
```

**Request Body:**
```typescript
interface QuestionnaireSubmission {
  patientId: string;
  questionnaireId: string;
  responses: QuestionResponse[];
  totalScore: number;
  isEligible: boolean;
  submittedAt: string;
}

interface QuestionResponse {
  questionKey: string;
  questionText: string;
  answerValue: string | string[] | number;
  answerText: string;
  score: number;
}
```

**Response:**
```json
{
  "success": true,
  "submissionId": "sub_123456",
  "result": {
    "isEligible": true,
    "totalScore": 45,
    "maxScore": 61,
    "percentage": 73.8,
    "status": "approved" | "rejected" | "pending_review"
  }
}
```

### 3. Get Patient-Specific Options (for Quantity Increase)
```
GET /api/questionnaires/quantity-increase/patient-options?patientId={id}
```

**Response:**
```json
{
  "availableStrengths": [
    {
      "value": "22",
      "label": "22% THC",
      "current": 10,
      "availableLevels": [15, 20, 25, 30]
    },
    {
      "value": "29",
      "label": "29% THC",
      "current": 15,
      "availableLevels": [20, 25, 30]
    }
  ]
}
```

### 4. Get Questionnaire Status
```
GET /api/questionnaires/{type}/status?patientId={id}
```

**Response:**
```json
{
  "hasSubmitted": true,
  "lastSubmission": {
    "submittedAt": "2024-01-15T10:30:00Z",
    "score": 45,
    "isEligible": true,
    "status": "approved"
  },
  "canResubmit": false,
  "nextAvailableDate": "2024-04-15T00:00:00Z"
}
```

## Implementation Notes

### Frontend Integration
1. **Dynamic Rendering**: Use the `type` field to determine which UI component to render
2. **Step Navigation**: Use `stepNumber` to control step progression
3. **Conditional Logic**: Check `conditionalOn` to show/hide questions based on dependencies
4. **Scoring**: Use `scoringMap` for real-time score calculation
5. **Validation**: Use `isRequired` for form validation

### Backend Considerations
1. **Version Control**: Track questionnaire versions for audit purposes
2. **Patient Context**: Some questionnaires need patient-specific data (treatment plans)
3. **Special Logic**: Handle custom scoring rules for specific questions
4. **Caching**: Cache questionnaire configurations for performance
5. **Validation**: Validate submissions against the current questionnaire configuration

### Key Mapping for Existing Components

#### Add22Thc.tsx
- Maps directly to `/api/questionnaires/add-22-thc/config`
- Uses `steps` array for step navigation
- Uses `scoringMap` for score calculation
- Handles conditional text fields via `conditionalOn`

#### ThcIncrease.tsx
- Maps directly to `/api/questionnaires/thc-increase/config`
- Special logic questions handled via `specialLogic` array
- 8 steps with multiple questions per step

#### ExtendTP.tsx
- Maps directly to `/api/questionnaires/extend-tp/config`
- 12 steps with one question each
- Consistent 5-point scoring scale

#### QuantityIncrease.tsx
- Maps to `/api/questionnaires/quantity-increase/config?patientId={id}`
- Requires patient-specific data for available quantities
- Complex first step with multiple question types

This specification allows the existing UI components to be driven entirely by backend configuration while maintaining their current look and functionality.
