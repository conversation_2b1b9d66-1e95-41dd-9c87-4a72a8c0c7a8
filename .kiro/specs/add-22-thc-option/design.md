# Design Document

## Overview

The Add 22% THC Option feature allows patients with existing 29% THC treatment plans to request adding a lower potency 22% THC option to their treatment regimen. This feature follows the established questionnaire pattern used in the THC Increase functionality but with questions and scoring specifically designed for adding a complementary lower-potency option rather than increasing potency.

The system will present a multi-step questionnaire, calculate eligibility scores, and integrate with the existing home page status display system to show request progress and outcomes.

## Architecture

### Component Structure
The feature follows the existing pattern established by `ThcIncrease.tsx`:

```
src/components/zenith/pages/
├── Add22ThcOption.tsx (new questionnaire component)
└── Home.tsx (updated to show 22% addition status)

src/types/
└── index.ts (updated with new interfaces)
```

### Data Flow
1. **Eligibility Check**: Home page verifies user has active 29% THC treatment plan
2. **Questionnaire Flow**: Multi-step form with validation and scoring
3. **Submission**: POST to new API endpoint with calculated scores
4. **Status Display**: Home page shows request status and outcomes

## Components and Interfaces

### Add22ThcOption Component

**Location**: `src/components/zenith/pages/Add22ThcOption.tsx`

**Key Features**:
- Multi-step questionnaire (5 main sections)
- Real-time form validation
- Scoring calculation
- Progress indicator
- Responsive design matching existing patterns

**State Management**:
```typescript
interface Add22ThcFormData {
  // Step 1: Reason for Request
  reasonForRequest: string[];  // Multiple selection
  reasonOther: string;
  
  // Step 2: Current 29% THC Response
  symptomImprovement: string;  // 1-10 scale
  currentSideEffects: string[];  // Multiple selection
  sideEffectsDescription: string;
  
  // Step 3: Health Changes
  healthChanges: string;  // yes/no
  healthChangesDescription: string;
  
  // Step 4: Expectations and Preferences
  expectations: string;  // Text field
  concerns: string;  // Text field
  usagePlan: string;  // Single selection
  usagePlanOther: string;
  
  // Step 5: Consent
  consent: string;  // yes/no
}

interface ScoringState {
  totalScore: number;
  maxScore: number;
  isEligible: boolean;
  questionScores: Record<string, number>;
}
```

### Scoring System

**Scoring Criteria**:
- **Maximum Score**: 35 points
- **Eligibility Threshold**: 20 points (57% of maximum)
- **Scoring Logic**: Rewards positive treatment response, minimal side effects, clear expectations, and informed consent

**Question Scoring**:

1. **Reason for Request** (0-8 points):
   - Side effects with 29%: 4 points
   - Prefer gentler effect: 3 points  
   - Different strain targeting: 2 points
   - Building tolerance: 1 point
   - Other: 1 point

2. **Symptom Improvement Scale** (0-10 points):
   - 1-3: 2 points (poor response, may benefit from alternative)
   - 4-6: 6 points (moderate response, good candidate)
   - 7-8: 10 points (good response, excellent candidate)
   - 9-10: 8 points (excellent response, may not need alternative)

3. **Current Side Effects** (0-6 points):
   - None: 6 points
   - Mild: 4 points
   - Moderate: 2 points
   - Strong: 0 points

4. **Health Changes** (0-3 points):
   - No changes: 3 points
   - Yes with description: 1 point

5. **Usage Plan** (0-4 points):
   - Alternative for specific times: 4 points
   - Rotation based on symptoms: 3 points
   - Unsure, want advice: 2 points
   - Other: 1 point

6. **Consent** (0-4 points):
   - Yes: 4 points
   - No: 0 points

### Home Page Integration

**Updates to Home.tsx**:

1. **New State Variables**:
```typescript
const [add22ThcQuestionnaireStatus, setAdd22ThcQuestionnaireStatus] = useState<{
  completed: boolean;
  score: number;
  isEligible: boolean;
  status?: string;
} | null>(null);
```

2. **Eligibility Logic**:
- Show "Add 22% THC Option" button only for patients with active 29% treatment plans
- Hide button if questionnaire already completed
- Show appropriate status based on questionnaire results

3. **Status Display**:
- **Pending**: Gray button with "Pending Doctor's Approval" text
- **Approved**: Green notification banner
- **Rejected**: Red button with denial dialog option
- **Not Started**: Green "Add 22% THC Option" button

## Data Models

### API Request/Response

**Submission Endpoint**: `POST /funnel/v1.0/patient/add-22-thc-questionnaire`

**Request Body**:
```typescript
interface Add22ThcSubmission {
  questionsAndAnswers: Array<{
    questionKey: string;
    questionText: string;
    answerValue: string | string[];
    answerText: string;
    score: number;
  }>;
  totalScore: number;
  maxScore: number;
  isEligible: boolean;
  submittedAt: string;
}
```

**Status Endpoint**: `GET /funnel/v1.0/patient/add-22-thc-questionnaire/status?email={email}`

**Response**:
```typescript
interface Add22ThcStatusResponse {
  success: boolean;
  questionnaire?: {
    totalScore: number;
    isEligible: boolean;
    status: 'submitted' | 'under_review' | 'approved' | 'rejected';
    submittedAt: string;
  };
}
```

## Error Handling

### Form Validation
- **Step Validation**: Each step requires all mandatory fields before proceeding
- **Multiple Selection**: Minimum one option required for checkbox groups
- **Text Fields**: Required when "Other" options are selected
- **Consent**: Must be "Yes" to proceed with submission

### API Error Handling
- **Network Errors**: Show retry option with user-friendly message
- **Validation Errors**: Display specific field errors
- **Server Errors**: Generic error message with support contact info
- **Timeout**: Retry mechanism with exponential backoff

### Eligibility Scenarios
- **Ineligible Score**: Show message about contacting medical team
- **Missing Treatment Plan**: Redirect to appropriate resources
- **Already Completed**: Show current status instead of form

## Testing Strategy

### Unit Tests
- **Scoring Logic**: Test all scoring combinations and edge cases
- **Form Validation**: Test required field validation and step progression
- **State Management**: Test form data updates and persistence
- **Component Rendering**: Test conditional rendering based on props/state

### Integration Tests
- **API Integration**: Test submission and status retrieval
- **Navigation Flow**: Test routing between components
- **Home Page Integration**: Test status display and button states
- **Error Scenarios**: Test network failures and error recovery

### User Acceptance Tests
- **Complete Questionnaire Flow**: End-to-end questionnaire completion
- **Eligibility Scenarios**: Test both eligible and ineligible outcomes
- **Status Tracking**: Test status display on home page
- **Mobile Responsiveness**: Test on various screen sizes
- **Accessibility**: Test keyboard navigation and screen reader compatibility

## Security Considerations

### Data Protection
- **Patient Data**: All form data encrypted in transit and at rest
- **Authentication**: Verify user authentication before questionnaire access
- **Authorization**: Ensure only patients with 29% plans can access feature
- **Session Management**: Handle session expiration gracefully

### Input Validation
- **Client-Side**: Validate all inputs before submission
- **Server-Side**: Re-validate all data on backend
- **Sanitization**: Clean text inputs to prevent XSS attacks
- **Rate Limiting**: Prevent spam submissions

## Performance Considerations

### Component Optimization
- **Lazy Loading**: Load component only when needed
- **Memoization**: Use React.memo for expensive re-renders
- **State Updates**: Batch state updates to minimize re-renders
- **Form Persistence**: Save form data to prevent loss on navigation

### API Optimization
- **Caching**: Cache questionnaire status to reduce API calls
- **Debouncing**: Debounce form auto-save functionality
- **Compression**: Compress API payloads for faster transmission
- **Error Recovery**: Implement retry logic for failed requests