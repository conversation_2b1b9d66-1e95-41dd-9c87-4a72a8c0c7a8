// Test to confirm all options are restored

const QUANTITY_LEVELS = [14, 28, 42, 56, 70, 84];

// Restored function - shows all levels above current
function getAvailableQuantityLevels(currentQuantity) {
  const currentIndex = QUANTITY_LEVELS.indexOf(currentQuantity);
  if (currentIndex === -1 || currentIndex === QUANTITY_LEVELS.length - 1) {
    return [];
  }
  return QUANTITY_LEVELS.slice(currentIndex + 1);
}

// Test cases
const testCases = [
  { current: 14, description: "Starting level (14g)" },
  { current: 28, description: "Second level (28g)" },
  { current: 42, description: "Third level (42g)" },
  { current: 56, description: "Fourth level (56g)" },
  { current: 70, description: "Fifth level (70g)" }
];

console.log("=== All Options Restored - Verification ===\n");

testCases.forEach(test => {
  const options = getAvailableQuantityLevels(test.current);
  const maxLevel = Math.max(...options);
  
  console.log(`${test.description}:`);
  console.log(`  Current: ${test.current}g`);
  console.log(`  Available options: [${options.join(', ')}]g`);
  console.log(`  UI Label: "22% THC (${test.current}g → up to ${maxLevel}g)"`);
  console.log(`  Max possible increase: +${maxLevel - test.current}g`);
  console.log("");
});

console.log("=== Confirmed Features ===");
console.log("✅ Users can select ANY quantity level above their current level");
console.log("✅ Maximum flexibility for quantity increases");
console.log("✅ 14g users can go directly to 84g if desired (+70g increase)");
console.log("✅ 28g users can go directly to 84g if desired (+56g increase)");
console.log("✅ All intermediate levels are available as options");

console.log("\n=== UI Examples ===");
console.log("14g patient sees: 28g, 42g, 56g, 70g, 84g options");
console.log("28g patient sees: 42g, 56g, 70g, 84g options");
console.log("42g patient sees: 56g, 70g, 84g options");
console.log("70g patient sees: 84g option");

console.log("\n🎯 Result: Full flexibility restored - users can choose any higher level!");
