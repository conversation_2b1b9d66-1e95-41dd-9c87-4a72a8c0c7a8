# Requirements Document

## Introduction

This feature enables patients with an existing 29% THC treatment plan to request adding a 22% THC option as an additional treatment choice. Unlike the existing THC increase questionnaire that moves patients from 22% to 29%, this questionnaire allows patients to add a lower potency option alongside their current higher potency treatment. The questionnaire focuses on current treatment experience, health changes, and usage preferences to ensure safety and suitability for dual-option treatment plans.

## Requirements

### Requirement 1

**User Story:** As a patient with an active 29% THC treatment plan, I want to request adding a 22% THC option to my treatment plan, so that I can have more flexibility in managing my symptoms with different potency levels.

#### Acceptance Criteria

1. WHEN a patient with an active 29% THC treatment plan accesses the questionnaire THEN the system SHALL present questions specific to adding a lower potency option
2. WHEN a patient completes the questionnaire THEN the system SHALL calculate a score based on their responses to determine eligibility
3. WHEN a patient submits the questionnaire THEN the system SHALL store their responses and eligibility status for doctor review

### Requirement 2

**User Story:** As a patient, I want to provide detailed information about my current 29% THC treatment experience, so that my doctor can assess whether adding a 22% option is appropriate for my situation.

#### Acceptance Criteria

1. WH<PERSON> answering reason questions THEN the system SHALL allow multiple selection options including side effects, preference for gentler effects, strain targeting, tolerance building, and other custom reasons
2. WHEN rating current treatment effectiveness THEN the system SHALL provide a 1-10 scale for symptom improvement assessment
3. WHEN reporting side effects THEN the system SHALL categorize responses as None, Mild, Moderate, or Strong with specific examples
4. WHEN indicating health changes THEN the system SHALL capture any new conditions, medications, or lifestyle changes since last consultation

### Requirement 3

**User Story:** As a patient, I want to specify my expectations and usage plans for the 22% THC option, so that my doctor understands how I intend to incorporate it into my treatment routine.

#### Acceptance Criteria

1. WHEN describing expectations THEN the system SHALL provide a text field for patients to explain what they hope to achieve
2. WHEN expressing concerns THEN the system SHALL provide a text field for patients to describe any worries about adding the option
3. WHEN planning usage THEN the system SHALL offer predefined options for how they plan to use 22% alongside 29% THC
4. WHEN providing consent THEN the system SHALL require explicit agreement for doctor review and MyHealth Record access

### Requirement 4

**User Story:** As a healthcare provider, I want to receive scored questionnaire results with detailed patient responses, so that I can make informed decisions about adding 22% THC options to existing treatment plans.

#### Acceptance Criteria

1. WHEN a questionnaire is submitted THEN the system SHALL calculate a total score based on predefined scoring criteria
2. WHEN scoring is complete THEN the system SHALL determine eligibility based on score thresholds
3. WHEN results are generated THEN the system SHALL include both quantitative scores and qualitative text responses
4. WHEN a patient is eligible THEN the system SHALL mark their request as approved for doctor review
5. WHEN a patient is not eligible THEN the system SHALL mark their request as requiring additional consultation

### Requirement 5

**User Story:** As a patient, I want to see the status of my 22% THC addition request on my home page, so that I can track the progress of my application.

#### Acceptance Criteria

1. WHEN a questionnaire is submitted and pending THEN the home page SHALL display a "pending approval" status for the 22% addition request
2. WHEN a request is approved THEN the home page SHALL display an approval notification with next steps
3. WHEN a request is rejected THEN the home page SHALL display information about contacting the medical team
4. WHEN no questionnaire has been completed THEN the home page SHALL show an option to "Add 22% THC Option" for eligible patients

### Requirement 6

**User Story:** As a system administrator, I want to ensure only patients with active 29% THC treatment plans can access the 22% addition questionnaire, so that the feature is only available to appropriate users.

#### Acceptance Criteria

1. WHEN a patient accesses the questionnaire THEN the system SHALL verify they have an active 29% THC treatment plan
2. WHEN a patient without a 29% plan attempts access THEN the system SHALL redirect them to appropriate resources
3. WHEN a patient has already completed the questionnaire THEN the system SHALL show their current status instead of allowing resubmission
4. WHEN a patient's treatment plan status changes THEN the system SHALL update their access permissions accordingly