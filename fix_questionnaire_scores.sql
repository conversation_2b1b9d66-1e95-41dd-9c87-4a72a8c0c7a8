-- Fix THC Increase Questionnaire Scores Only
-- This script recalculates and updates individual question scores for existing THC increase questionnaire records

-- First, let's check what records we have
SELECT
    id,
    email,
    total_score,
    is_eligible,
    created_at,
    jsonb_array_length(questionnaire_data->'questionsAndAnswers') as question_count,
    (questionnaire_data->'questionsAndAnswers'->0->>'score') as first_question_score
FROM thc_increase_questionnaire
ORDER BY created_at DESC
LIMIT 10;

-- Check if any records have zero scores
SELECT
    COUNT(*) as total_records,
    COUNT(CASE WHEN total_score = 0 THEN 1 END) as zero_total_score,
    COUNT(CASE WHEN (questionnaire_data->'questionsAndAnswers'->0->>'score')::INTEGER = 0 THEN 1 END) as zero_first_question_score
FROM thc_increase_questionnaire;

-- =====================================================
-- 1. Fix THC Increase Questionnaire Scores
-- =====================================================

-- Function to calculate THC increase question scores
CREATE OR REPLACE FUNCTION calculate_thc_increase_scores(questionnaire_data JSONB)
RETURNS JSONB AS $$
DECLARE
    questions_array JSONB;
    question JSONB;
    updated_questions JSONB := '[]'::JSONB;
    question_key TEXT;
    answer_value TEXT;
    score INTEGER;
BEGIN
    -- Get the questionsAndAnswers array
    questions_array := questionnaire_data->'questionsAndAnswers';
    
    -- Loop through each question and recalculate score
    FOR question IN SELECT * FROM jsonb_array_elements(questions_array)
    LOOP
        question_key := question->>'questionKey';
        answer_value := question->>'answerValue';
        score := 0;
        
        -- Calculate score based on question key and answer
        CASE question_key
            WHEN 'consistency' THEN
                CASE answer_value
                    WHEN 'every-day' THEN score := 3;
                    WHEN 'most-days' THEN score := 2;
                    WHEN 'few-times-week' THEN score := 1;
                    WHEN 'once-week' THEN score := 0;
                    ELSE score := 0;
                END CASE;
            
            WHEN 'dosage' THEN
                CASE answer_value
                    WHEN 'less-than-0.5g' THEN score := 0;
                    WHEN '0.5g-1g' THEN score := 1;
                    WHEN '1g-2g' THEN score := 2;
                    WHEN 'more-than-2g' THEN score := 4;
                    ELSE score := 0;
                END CASE;
            
            WHEN 'frequency' THEN
                CASE answer_value
                    WHEN 'once-a-day' THEN score := 1;
                    WHEN 'twice-a-day' THEN score := 2;
                    WHEN 'three-times-a-day' THEN score := 3;
                    WHEN 'more-than-three' THEN score := 0;
                    ELSE score := 0;
                END CASE;
            
            WHEN 'condition' THEN
                -- Special scoring: 2 points if not 'other'
                IF answer_value != 'other' AND answer_value IS NOT NULL THEN
                    score := 2;
                ELSE
                    score := 0;
                END IF;
            
            WHEN 'effectiveness' THEN
                CASE answer_value
                    WHEN '1-2' THEN score := 0;
                    WHEN '3-4' THEN score := 1;
                    WHEN '5-6' THEN score := 2;
                    WHEN '7-8' THEN score := 3;
                    WHEN '9-10' THEN score := 4;
                    ELSE score := 0;
                END CASE;
            
            WHEN 'symptomChanges' THEN
                CASE answer_value
                    WHEN 'significant-improvement' THEN score := 4;
                    WHEN 'some-improvement' THEN score := 3;
                    WHEN 'no-change' THEN score := 2;
                    WHEN 'worsening-symptoms' THEN score := 0;
                    ELSE score := 0;
                END CASE;
            
            WHEN 'sideEffect' THEN
                -- Special scoring for side effects
                IF answer_value = 'none' THEN
                    score := 4;
                ELSIF answer_value != 'other' AND answer_value IS NOT NULL THEN
                    score := 1;
                ELSE
                    score := 0;
                END IF;
            
            WHEN 'sideEffectManageability' THEN
                CASE answer_value
                    WHEN '1-2' THEN score := 0;
                    WHEN '3-4' THEN score := 1;
                    WHEN '5-6' THEN score := 2;
                    WHEN '7-8' THEN score := 3;
                    WHEN '9-10' THEN score := 4;
                    ELSE score := 0;
                END CASE;
            
            WHEN 'concerns' THEN
                CASE answer_value
                    WHEN 'no' THEN score := 3;
                    WHEN 'yes' THEN score := 0;
                    ELSE score := 0;
                END CASE;
            
            WHEN 'treatmentEffectiveness' THEN
                CASE answer_value
                    WHEN 'very-effective' THEN score := 4;
                    WHEN 'somewhat-effective' THEN score := 3;
                    WHEN 'not-effective' THEN score := 0;
                    ELSE score := 0;
                END CASE;
            
            WHEN 'weaknessAssessment' THEN
                CASE answer_value
                    WHEN 'yes-definitely' THEN score := 4;
                    WHEN 'yes-somewhat' THEN score := 3;
                    WHEN 'no-satisfied' THEN score := 0;
                    ELSE score := 0;
                END CASE;
            
            WHEN 'insufficientRelief' THEN
                CASE answer_value
                    WHEN 'yes-definitely' THEN score := 4;
                    WHEN 'yes-somewhat' THEN score := 3;
                    WHEN 'no-adequate' THEN score := 0;
                    ELSE score := 0;
                END CASE;
            
            WHEN 'satisfactionWithForm' THEN
                CASE answer_value
                    WHEN 'very-satisfied' THEN score := 4;
                    WHEN 'somewhat-satisfied' THEN score := 3;
                    WHEN 'neutral' THEN score := 2;
                    WHEN 'somewhat-unsatisfied' THEN score := 1;
                    WHEN 'very-unsatisfied' THEN score := 0;
                    ELSE score := 0;
                END CASE;
            
            WHEN 'openToHigherPotency' THEN
                CASE answer_value
                    WHEN 'yes' THEN score := 3;
                    WHEN 'maybe' THEN score := 2;
                    WHEN 'no' THEN score := 0;
                    ELSE score := 0;
                END CASE;
            
            WHEN 'quickReliefImportance' THEN
                CASE answer_value
                    WHEN 'very-important' THEN score := 4;
                    WHEN 'somewhat-important' THEN score := 3;
                    WHEN 'neutral' THEN score := 2;
                    WHEN 'not-important' THEN score := 0;
                    ELSE score := 0;
                END CASE;
            
            WHEN 'continueTreatment' THEN
                CASE answer_value
                    WHEN 'very-likely' THEN score := 3;
                    WHEN 'somewhat-likely' THEN score := 2;
                    WHEN 'neutral' THEN score := 1;
                    WHEN 'unlikely' THEN score := 0;
                    ELSE score := 0;
                END CASE;
            
            WHEN 'overallSatisfaction' THEN
                CASE answer_value
                    WHEN 'very-satisfied' THEN score := 4;
                    WHEN 'somewhat-satisfied' THEN score := 3;
                    WHEN 'neutral' THEN score := 2;
                    WHEN 'somewhat-unsatisfied' THEN score := 1;
                    WHEN 'very-unsatisfied' THEN score := 0;
                    ELSE score := 0;
                END CASE;
            
            ELSE
                score := 0;
        END CASE;
        
        -- Update the question with the calculated score
        updated_questions := updated_questions || jsonb_build_object(
            'questionKey', question->>'questionKey',
            'questionText', question->>'questionText',
            'answerValue', question->>'answerValue',
            'answerText', question->>'answerText',
            'score', score
        );
    END LOOP;
    
    -- Return updated questionnaire data
    RETURN jsonb_set(questionnaire_data, '{questionsAndAnswers}', updated_questions);
END;
$$ LANGUAGE plpgsql;

-- Update THC Increase questionnaire records
UPDATE thc_increase_questionnaire 
SET 
    questionnaire_data = calculate_thc_increase_scores(questionnaire_data),
    updated_at = NOW()
WHERE 
    -- Only update records where scores are likely incorrect (all zeros or very low)
    total_score < 10 
    OR (questionnaire_data->'questionsAndAnswers'->0->>'score')::INTEGER = 0;

-- Recalculate total scores and eligibility for THC Increase
UPDATE thc_increase_questionnaire 
SET 
    total_score = (
        SELECT COALESCE(SUM((question->>'score')::INTEGER), 0)
        FROM jsonb_array_elements(questionnaire_data->'questionsAndAnswers') AS question
    ),
    is_eligible = (
        SELECT COALESCE(SUM((question->>'score')::INTEGER), 0) >= 45  -- Keep original 45 threshold
        FROM jsonb_array_elements(questionnaire_data->'questionsAndAnswers') AS question
    ),
    updated_at = NOW()
WHERE 
    total_score < 10 
    OR (questionnaire_data->'questionsAndAnswers'->0->>'score')::INTEGER = 0;

-- =====================================================
-- 2. Fix Add 22% THC Questionnaire Scores  
-- =====================================================

-- Function to calculate Add 22% THC question scores
CREATE OR REPLACE FUNCTION calculate_add_22_thc_scores(questionnaire_data JSONB)
RETURNS JSONB AS $$
DECLARE
    questions_array JSONB;
    question JSONB;
    updated_questions JSONB := '[]'::JSONB;
    question_key TEXT;
    answer_value TEXT;
    score INTEGER;
BEGIN
    -- Get the questionsAndAnswers array
    questions_array := questionnaire_data->'questionsAndAnswers';
    
    -- Loop through each question and recalculate score
    FOR question IN SELECT * FROM jsonb_array_elements(questions_array)
    LOOP
        question_key := question->>'questionKey';
        answer_value := question->>'answerValue';
        score := 0;
        
        -- Calculate score based on question key and answer
        CASE question_key
            WHEN 'reasonSideEffects' THEN
                IF (question->>'answerValue')::BOOLEAN = true THEN score := 4; ELSE score := 0; END IF;
            
            WHEN 'reasonGentlerEffect' THEN
                IF (question->>'answerValue')::BOOLEAN = true THEN score := 3; ELSE score := 0; END IF;
            
            WHEN 'reasonDifferentStrain' THEN
                IF (question->>'answerValue')::BOOLEAN = true THEN score := 2; ELSE score := 0; END IF;
            
            WHEN 'reasonTolerance' THEN
                IF (question->>'answerValue')::BOOLEAN = true THEN score := 3; ELSE score := 0; END IF;
            
            WHEN 'reasonOther' THEN
                IF (question->>'answerValue')::BOOLEAN = true THEN score := 1; ELSE score := 0; END IF;
            
            WHEN 'symptomImprovement' THEN
                CASE answer_value
                    WHEN '1', '2' THEN score := 0;
                    WHEN '3', '4' THEN score := 1;
                    WHEN '5', '6' THEN score := 2;
                    WHEN '7', '8' THEN score := 3;
                    WHEN '9', '10' THEN score := 4;
                    ELSE score := 0;
                END CASE;
            
            WHEN 'sideEffectsNone' THEN
                IF (question->>'answerValue')::BOOLEAN = true THEN score := 4; ELSE score := 0; END IF;
            
            WHEN 'sideEffectsMild' THEN
                IF (question->>'answerValue')::BOOLEAN = true THEN score := 2; ELSE score := 0; END IF;
            
            WHEN 'sideEffectsModerate' THEN
                IF (question->>'answerValue')::BOOLEAN = true THEN score := 1; ELSE score := 0; END IF;
            
            WHEN 'sideEffectsStrong' THEN
                IF (question->>'answerValue')::BOOLEAN = true THEN score := 0; ELSE score := 0; END IF;
            
            WHEN 'healthChanges' THEN
                CASE answer_value
                    WHEN 'no-changes' THEN score := 3;
                    WHEN 'yes' THEN score := 1;
                    ELSE score := 0;
                END CASE;
            
            WHEN 'usagePlan' THEN
                CASE answer_value
                    WHEN 'alternative-situations' THEN score := 4;
                    WHEN 'rotation-time-symptoms' THEN score := 3;
                    WHEN 'unsure-advice' THEN score := 2;
                    WHEN 'other' THEN score := 1;
                    ELSE score := 0;
                END CASE;
            
            WHEN 'consent' THEN
                CASE answer_value
                    WHEN 'yes' THEN score := 5;
                    WHEN 'no' THEN score := 0;
                    ELSE score := 0;
                END CASE;
            
            ELSE
                score := 0;
        END CASE;
        
        -- Update the question with the calculated score
        updated_questions := updated_questions || jsonb_build_object(
            'questionKey', question->>'questionKey',
            'questionText', question->>'questionText',
            'answerValue', question->>'answerValue',
            'answerText', question->>'answerText',
            'score', score
        );
    END LOOP;
    
    -- Return updated questionnaire data
    RETURN jsonb_set(questionnaire_data, '{questionsAndAnswers}', updated_questions);
END;
$$ LANGUAGE plpgsql;
