# THC Increase Questionnaire Scoring Implementation TODO

## Analysis Summary

### Current State
- The ThcIncrease.tsx component has 8 steps with multiple questions
- Currently no form state management or data collection
- No scoring system implemented
- No API submission logic
- Questions are present but answers are not being captured

### Scoring Model Requirements
- Total possible score: 61 points (updated based on existing questions)
- Eligibility threshold: 45+ points for 29% THC treatment
- 17 questions with varying point values (0-4 points each)
- **IMPORTANT**: Users select ONE option per question (no multi-select)

## Implementation Plan

### Phase 1: Form State Management
- [ ] **Add form data state** - Create comprehensive state object to store all answers
- [ ] **Add scoring state** - Track current total score and individual question scores
- [ ] **Add form validation** - Ensure all questions are answered before proceeding
- [ ] **Add answer handlers** - Implement onChange handlers for all radio groups

### Phase 2: Scoring Logic Implementation
- [ ] **Create scoring mapping object** - Map each answer value to its point value
- [ ] **Implement score calculation function** - Calculate total score based on answers
- [ ] **Add real-time scoring** - Update score as user answers questions
- [ ] **Add eligibility determination** - Check if score >= 45 for 29% THC eligibility

### Phase 3: Question-Answer Mapping
Based on analysis, here are the questions that need scoring implementation:

#### Step 1 Questions:
1. **Consistency** (Q1): Every day (3), Most days (2), Occasionally (1), Rarely (0)
2. **Dosage** (Q2): <0.5g (1), 0.5-1g (2), 1-2g (3), >2g (4)
3. **Frequency** (Q3): Once (1), Twice (2), Three times (3), As needed (1)

#### Step 2 Questions:
4. **Condition** (Q4): Select ONE condition = 2 points (Chronic pain, Anxiety, Insomnia, Inflammation, Other)
5. **Effectiveness Scale** (Q5): 1-2 (0), 3-4 (1), 5-6 (2), 7-8 (3), 9-10 (4)

#### Step 3 Questions:
6. **Symptom Changes** (Q6): Significant improvement (4), Some improvement (2), No change (1), Worsening (0)
7. **Side Effects** (Q7): Select ONE option - Drowsiness (1), Dry mouth (1), Dizziness (1), Increased heart rate (1), Anxiety (1), None (4), Other (1)

#### Step 4 Questions:
8. **Side Effect Manageability** (Q8): 1-2 (0), 3-4 (1), 5-6 (2), 7-8 (3), 9-10 (4)
9. **Concerns/Issues** (Q9): Yes (0), No (3)

#### Step 5 Questions:
11. **Treatment Effectiveness** (Q11): Very effective (4), Effective (3), Somewhat (2), Not effective (0)
12. **Weakness Assessment** (Q12): Yes definitely (4), Yes somewhat (3), No adequate (1), No too strong (0)

#### Step 6 Questions:
14. **Insufficient Relief** (Q14): Yes definitely (4), Yes somewhat (3), No adequate (1), Complete relief (0)
15. **Satisfaction with Form** (Q15): Very satisfied (4), Satisfied (3), Neutral (2), Unsatisfied (1), Very unsatisfied (0)

#### Step 7 Questions:
16. **Open to Higher Potency** (Q16): Yes (3), No (0), Maybe (1)
17. **Quick Relief Importance** (Q17): Very important (4), Important (3), Neutral (2), Not important (1)

#### Step 8 Questions:
18. **Continue Treatment** (Q18): Very likely (3), Likely (2), Neutral (1), Unlikely (0), Very unlikely (0)
19. **Overall Satisfaction** (Q19): Very satisfied (4), Satisfied (3), Neutral (2), Unsatisfied (1), Very unsatisfied (0)

### Phase 4: Missing Questions Analysis
**Note**: Some questions from the scoring model are missing from the current implementation:
- Q10: Tolerance development
- Q13: Breakout pain frequency
- General information fields (Name, Age, Gender, Date, Start Date)

**Decision**: As requested, we will NOT add missing questions, only implement scoring for existing questions.

### Phase 5: UI Enhancements
- [ ] **Add score display** - Show current score and eligibility status
- [ ] **Add progress indicators** - Visual feedback on completion
- [ ] **Add validation feedback** - Show which questions need answers
- [ ] **Add results page** - Display final score and eligibility determination

### Phase 6: Data Persistence & API Integration
- [ ] **Create TypeScript interface** - Define ThcIncreaseQuestionnaire type
- [ ] **Add API submission** - Submit answers and score to backend
- [ ] **Add error handling** - Handle submission failures gracefully
- [ ] **Add loading states** - Show loading during submission
- [ ] **Add success feedback** - Confirm successful submission

### Phase 7: Technical Implementation Details

#### State Structure:
```typescript
interface ThcIncreaseFormData {
  consistency: string;
  dosage: string;
  frequency: string;
  conditions: string[];
  conditionOther: string;
  effectiveness: string;
  symptomChanges: string;
  sideEffects: string[];
  sideEffectsOther: string;
  sideEffectManageability: string;
  concerns: string;
  treatmentEffectiveness: string;
  weaknessAssessment: string;
  insufficientRelief: string;
  satisfactionWithForm: string;
  openToHigherPotency: string;
  quickReliefImportance: string;
  continueeTreatment: string;
  overallSatisfaction: string;
}

interface ScoringState {
  totalScore: number;
  maxScore: number;
  isEligible: boolean;
  questionScores: Record<string, number>;
}
```

#### Key Functions to Implement:
- `calculateQuestionScore(questionKey: string, answer: string): number`
- `calculateTotalScore(formData: ThcIncreaseFormData): number`
- `checkEligibility(score: number): boolean`
- `handleAnswerChange(questionKey: string, value: string): void`
- `submitQuestionnaire(): Promise<void>`

### Phase 8: Testing & Validation
- [ ] **Test scoring calculations** - Verify all point calculations are correct
- [ ] **Test edge cases** - Handle incomplete forms, invalid answers
- [ ] **Test API integration** - Ensure data is submitted correctly
- [ ] **Test eligibility logic** - Verify 45+ point threshold works
- [ ] **Test user experience** - Ensure smooth flow through all steps

## Updated Maximum Score Calculation (Single Select Only):
- Step 1: 10 points (3+4+3)
- Step 2: 6 points (2+4) - Only ONE condition selected
- Step 3: 8 points (4+4) - Only ONE side effect selected
- Step 4: 7 points (4+3)
- Step 5: 8 points (4+4)
- Step 6: 8 points (4+4)
- Step 7: 7 points (3+4)
- Step 8: 7 points (3+4)

**TOTAL MAXIMUM: 61 points** ✅

## Implementation Priority
1. **High Priority**: Form state management and scoring logic ✅ COMPLETED
2. **Medium Priority**: UI enhancements and validation ✅ COMPLETED
3. **Low Priority**: API integration and advanced features

## Notes
- Follow existing codebase patterns from other questionnaire forms ✅
- Use similar state management approach as FormQuestionaire.tsx ✅
- Maintain existing UI/UX design patterns ✅
- Ensure mobile responsiveness is preserved ✅
- Add proper TypeScript typing throughout ✅

## CORRECTION NEEDED:
Current implementation uses checkboxes for conditions and side effects, but should use radio buttons for single selection only.
