-- Migration: Create ExtendTP Questionnaire Table
-- This table stores ExtendTP questionnaire submissions with JSONB for flexible data storage

CREATE TABLE IF NOT EXISTS extend_tp_questionnaire (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),

    -- Patient identification (following existing patterns)
    patient_id VARCHAR REFERENCES patient("patientID"),
    email TEXT NOT NULL,
    zoho_id TEXT,

    -- Questionnaire data stored as JSONB for flexibility
    questionnaire_data JSONB NOT NULL,

    -- Scoring information
    total_score INTEGER NOT NULL DEFAULT 0,
    max_score INTEGER NOT NULL DEFAULT 60,
    is_eligible BOOLEAN NOT NULL DEFAULT FALSE,

    -- Status tracking
    status TEXT NOT NULL DEFAULT 'submitted', -- submitted, under_review, approved, rejected
    reviewed_by TEXT, -- doctor ID who reviewed
    reviewed_at TIMESTAMP WITH TIME ZONE,
    review_notes TEXT,

    -- Approval tracking
    approved_by TEXT, -- doctor ID who approved
    approved_at TIMESTAMP WITH TIME ZONE,
    approval_notes TEXT,

    -- Submission metadata
    ip_address TEXT,
    user_agent TEXT,

    -- Timestamps (following existing patterns)
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_extend_tp_questionnaire_patient_id ON extend_tp_questionnaire(patient_id);
CREATE INDEX IF NOT EXISTS idx_extend_tp_questionnaire_email ON extend_tp_questionnaire(email);
CREATE INDEX IF NOT EXISTS idx_extend_tp_questionnaire_status ON extend_tp_questionnaire(status);
CREATE INDEX IF NOT EXISTS idx_extend_tp_questionnaire_is_eligible ON extend_tp_questionnaire(is_eligible);
CREATE INDEX IF NOT EXISTS idx_extend_tp_questionnaire_created_at ON extend_tp_questionnaire(created_at);

-- Create GIN index for JSONB data for efficient querying
CREATE INDEX IF NOT EXISTS idx_extend_tp_questionnaire_data_gin ON extend_tp_questionnaire USING GIN (questionnaire_data);

-- Add trigger to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION update_extend_tp_questionnaire_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_extend_tp_questionnaire_updated_at
    BEFORE UPDATE ON extend_tp_questionnaire
    FOR EACH ROW
    EXECUTE FUNCTION update_extend_tp_questionnaire_updated_at();

-- Add comments for documentation
COMMENT ON TABLE extend_tp_questionnaire IS 'Stores ExtendTP questionnaire submissions with scoring and approval workflow';
COMMENT ON COLUMN extend_tp_questionnaire.questionnaire_data IS 'JSONB containing all questionnaire answers and metadata';
COMMENT ON COLUMN extend_tp_questionnaire.total_score IS 'Calculated total score from questionnaire answers (0-60)';
COMMENT ON COLUMN extend_tp_questionnaire.is_eligible IS 'Whether patient is eligible for 6-month treatment plan (score >= 42)';
COMMENT ON COLUMN extend_tp_questionnaire.status IS 'Current status: submitted, under_review, approved, rejected';
COMMENT ON COLUMN extend_tp_questionnaire.ip_address IS 'IP address of the user who submitted the questionnaire';
COMMENT ON COLUMN extend_tp_questionnaire.user_agent IS 'User agent string of the browser used to submit the questionnaire';

-- Example of the JSONB structure that will be stored:
/*
questionnaire_data JSONB structure:
{
  "questionsAndAnswers": [
    {
      "questionKey": "adherence",
      "answerValue": "always-followed",
      "score": 5
    },
    {
      "questionKey": "symptomImprovement",
      "answerValue": "significant-improvement",
      "score": 5
    },
    {
      "questionKey": "symptomFrequency",
      "answerValue": "much-less-often",
      "score": 5
    },
    {
      "questionKey": "additionalRelief",
      "answerValue": "no-none",
      "score": 5
    },
    {
      "questionKey": "functionalBenefit",
      "answerValue": "significantly-improved",
      "score": 5
    },
    {
      "questionKey": "sleepQuality",
      "answerValue": "much-improved",
      "score": 5
    },
    {
      "questionKey": "tolerance",
      "answerValue": "no-increase-needed",
      "score": 5
    },
    {
      "questionKey": "sideEffectSeverity",
      "answerValue": "none-mild",
      "score": 5
    },
    {
      "questionKey": "sideEffectTolerability",
      "answerValue": "not-at-all",
      "score": 5
    },
    {
      "questionKey": "overallSatisfaction",
      "answerValue": "very-satisfied",
      "score": 5
    },
    {
      "questionKey": "goalAchievement",
      "answerValue": "completely-met",
      "score": 5
    },
    {
      "questionKey": "treatmentIntent",
      "answerValue": "continue-current",
      "score": 5
    }
  ],
  "metadata": {
    "submittedAt": "2025-01-07T10:30:00Z",
    "ipAddress": "***********",
    "userAgent": "Mozilla/5.0...",
    "completionTimeMinutes": 12
  }
}
*/
