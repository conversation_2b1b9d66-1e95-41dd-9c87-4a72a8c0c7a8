{"questionnaire": "Add 22% THC Option", "description": "Questionnaire for patients requesting to add a 22% THC product to their existing 29% THC treatment plan", "totalSteps": 5, "maxScore": 33, "passingScore": 7, "questions": [{"step": 1, "questionKey": "reasonForRequest", "questionText": "Why do you want to add a 22% THC product to your treatment plan? (Select all that apply)", "questionType": "checkbox_multiple", "options": [{"key": "reasonSideEffects", "text": "Experiencing side effects with 29% (e.g., anxiety, dizziness, increased heart rate)", "score": 4}, {"key": "reasonGentlerEffect", "text": "Prefer a gentler effect for daily or daytime use", "score": 3}, {"key": "reasonDifferentStrain", "text": "Trying a different strain for symptom targeting (e.g., sleep, focus, mood)", "score": 2}, {"key": "reasonTolerance", "text": "Building tolerance to higher THC strain", "score": 3}, {"key": "reasonOther", "text": "Other (please describe):", "score": 1, "hasTextField": true, "textFieldKey": "reasonOtherText", "textFieldPlaceholder": "Please describe your other reason..."}]}, {"step": 2, "questionKey": "currentResponse", "questionText": "How well has your current 29% THC treatment worked for you?", "questionType": "slider_and_checkbox", "parts": [{"partKey": "symptomImprovement", "partText": "Rate your symptom improvement on a scale of 1-10 (1 = no improvement, 10 = complete relief):", "type": "slider", "min": 1, "max": 10, "step": 1, "scoring": {"1": 0, "2": 0, "3": 1, "4": 1, "5": 2, "6": 2, "7": 3, "8": 3, "9": 4, "10": 4}, "labels": {"1": "No improvement", "2": "No improvement", "3": "Minimal improvement", "4": "Minimal improvement", "5": "Some improvement", "6": "Some improvement", "7": "Good improvement", "8": "Good improvement", "9": "Very effective", "10": "Complete relief"}}, {"partKey": "sideEffects", "partText": "Have you experienced any side effects with the 29% THC product? (Select all that apply)", "type": "checkbox_multiple", "options": [{"key": "sideEffectsNone", "text": "None", "score": 4}, {"key": "sideEffectsMild", "text": "Mild (e.g., dry mouth, mild sedation, tiredness)", "score": 2}, {"key": "sideEffectsModerate", "text": "Moderate (e.g., dizziness, nausea, appetite changes)", "score": 1, "requiresDescription": true}, {"key": "sideEffectsStrong", "text": "Strong (e.g., anxiety, mood changes, racing heart, confusion)", "score": 0, "requiresDescription": true}], "conditionalTextField": {"key": "sideEffectsDescription", "placeholder": "Please describe briefly...", "showWhen": ["sideEffectsModerate", "sideEffectsStrong"], "required": true}}]}, {"step": 3, "questionKey": "healthChanges", "questionText": "Have there been any changes in your health, medications, or lifestyle since your last consultation?", "questionType": "radio_with_conditional", "subtitle": "(e.g., new conditions, medications, substance use, mental health updates)", "options": [{"value": "no-changes", "text": "No changes", "score": 3}, {"value": "yes", "text": "Yes — please describe:", "score": 1, "hasTextField": true, "textFieldKey": "healthChangesDescription", "textFieldPlaceholder": "Please describe the changes..."}], "note": "Note: If changes involve blood pressure, heart rate, or new medications, we may ask for a quick pharmacy reading or health app screenshot for safety."}, {"step": 4, "questionKey": "expectationsAndPreferences", "questionText": "Expectations and preferences", "questionType": "text_and_radio", "parts": [{"partKey": "expectations", "partText": "What do you hope to achieve by adding a 22% THC product? (e.g., reduced side effects, better daily functioning)", "type": "textarea", "placeholder": "Please describe your expectations...", "required": true, "score": 0}, {"partKey": "concerns", "partText": "Do you have any concerns about adding this option? (e.g., potential interactions or reduced effectiveness)", "type": "textarea", "placeholder": "Please describe any concerns...", "required": true, "score": 0}, {"partKey": "usagePlan", "partText": "How do you plan to use the 22% THC product alongside your 29%?", "type": "radio", "required": true, "options": [{"value": "alternative-situations", "text": "As an alternative for specific times/situations", "score": 4}, {"value": "rotation-time-symptoms", "text": "In rotation with 29% depending on time of day or symptoms", "score": 3}, {"value": "unsure-advice", "text": "Unsure – would like advice from my doctor", "score": 2}, {"value": "other", "text": "Other (please describe):", "score": 1}]}]}, {"step": 5, "questionKey": "consent", "questionText": "Do you consent to your doctor reviewing this information, accessing your MyHealth Record if needed, and updating your treatment plan if appropriate?", "questionType": "radio", "options": [{"value": "yes", "text": "Yes", "score": 5}, {"value": "no", "text": "No", "score": 0}]}], "formDataStructure": {"reasonSideEffects": "boolean", "reasonGentlerEffect": "boolean", "reasonDifferentStrain": "boolean", "reasonTolerance": "boolean", "reasonOther": "boolean", "reasonOtherText": "string", "symptomImprovement": "string (1-10)", "sideEffectsNone": "boolean", "sideEffectsMild": "boolean", "sideEffectsModerate": "boolean", "sideEffectsStrong": "boolean", "sideEffectsDescription": "string", "healthChanges": "string (no-changes|yes)", "healthChangesDescription": "string", "expectations": "string", "concerns": "string", "usagePlan": "string (alternative-situations|rotation-time-symptoms|unsure-advice|other)", "consent": "string (yes|no)"}, "validationRules": {"step1": "At least one reason must be selected", "step2": "Symptom improvement rating and at least one side effect option. Description required if moderate or strong side effects selected", "step3": "Health changes selection required", "step4": "All three fields must be filled (expectations, concerns, usage plan)", "step5": "Consent must be given"}, "scoringNotes": {"totalMaxScore": 33, "passingThreshold": 7, "passingPercentage": "20% of maximum score", "eligibilityMessage": "Score >= 7 points qualifies for approval", "nonScoringFields": ["reasonOtherText", "sideEffectsDescription", "healthChangesDescription", "expectations", "concerns"]}}