-- Simple debug script for THC Increase questionnaire
-- Run these queries one by one to understand the current state

-- 1. Check if table exists and has data
SELECT COUNT(*) as record_count FROM thc_increase_questionnaire;

-- 2. Check the structure of one record
SELECT 
    id,
    email,
    total_score,
    is_eligible,
    created_at,
    questionnaire_data->'questionsAndAnswers' as questions_array
FROM thc_increase_questionnaire 
LIMIT 1;

-- 3. Check if questionsAndAnswers exists and what it looks like
SELECT 
    id,
    jsonb_array_length(questionnaire_data->'questionsAndAnswers') as question_count,
    questionnaire_data->'questionsAndAnswers'->0 as first_question
FROM thc_increase_questionnaire 
LIMIT 3;

-- 4. Check current scores in the data
SELECT 
    id,
    email,
    total_score,
    (questionnaire_data->'questionsAndAnswers'->0->>'score') as first_score,
    (questionnaire_data->'questionsAndAnswers'->1->>'score') as second_score
FROM thc_increase_questionnaire 
LIMIT 5;

-- 5. Simple update test - just update one record to see if it works
-- First, let's see what we're working with
SELECT 
    id,
    email,
    total_score,
    questionnaire_data->'questionsAndAnswers'->0->>'questionKey' as first_question_key,
    questionnaire_data->'questionsAndAnswers'->0->>'answerValue' as first_answer_value,
    questionnaire_data->'questionsAndAnswers'->0->>'score' as first_current_score
FROM thc_increase_questionnaire 
ORDER BY created_at DESC
LIMIT 1;
