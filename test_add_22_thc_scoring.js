// Test file for Add 22% THC Questionnaire Scoring Logic
// This tests the updated eligibility threshold of 7 points (20% of 33 max points)

console.log("=== Add 22% THC Questionnaire Scoring Tests ===\n");

// Scoring mapping object - matches the component implementation
const scoringMap = {
  // Question 1: Reasons (checkbox values)
  reasonSideEffects: { true: 4, false: 0 },
  reasonGentlerEffect: { true: 3, false: 0 },
  reasonDifferentStrain: { true: 2, false: 0 },
  reasonTolerance: { true: 3, false: 0 },
  reasonOther: { true: 1, false: 0 },

  // Question 2: Symptom improvement (1-10 scale)
  symptomImprovement: {
    "1": 0, "2": 0, "3": 1, "4": 1, "5": 2,
    "6": 2, "7": 3, "8": 3, "9": 4, "10": 4
  },

  // Question 2: Side effects (checkbox values)
  sideEffectsNone: { true: 4, false: 0 },
  sideEffectsMild: { true: 2, false: 0 },
  sideEffectsModerate: { true: 1, false: 0 },
  sideEffectsStrong: { true: 0, false: 0 },

  // Question 3: Health changes
  healthChanges: {
    "no-changes": 3,
    "yes": 1
  },

  // Question 4: Usage plan
  usagePlan: {
    "alternative-situations": 4,
    "rotation-time-symptoms": 3,
    "unsure-advice": 2,
    "other": 1
  },

  // Question 5: Consent
  consent: {
    "yes": 5,
    "no": 0
  }
};

// Calculate score for individual question
function calculateQuestionScore(questionKey, answer) {
  const questionScoring = scoringMap[questionKey];
  if (!questionScoring) return 0;

  const answerKey = typeof answer === 'boolean' ? answer.toString() : answer;
  return questionScoring[answerKey] || 0;
}

// Calculate total score from form data
function calculateTotalScore(formData) {
  let totalScore = 0;
  const questionScores = {};

  // Calculate score for each question
  Object.entries(formData).forEach(([key, value]) => {
    // Skip text fields that don't contribute to scoring
    if (key !== 'reasonOtherText' && key !== 'sideEffectsDescription' && 
        key !== 'healthChangesDescription' && key !== 'expectations' && key !== 'concerns') {
      const score = calculateQuestionScore(key, value);
      questionScores[key] = score;
      totalScore += score;
    }
  });

  return { totalScore, questionScores };
}

// Check eligibility based on score (UPDATED THRESHOLD: 7 points = 20%)
function checkEligibility(score) {
  return score >= 7; // 20% of 33 points
}

// Test cases
const testCases = [
  {
    name: "Maximum Score Test",
    description: "All highest scoring options selected",
    formData: {
      // Question 1: All reasons selected (max: 13 points)
      reasonSideEffects: true,     // 4 points
      reasonGentlerEffect: true,   // 3 points
      reasonDifferentStrain: true, // 2 points
      reasonTolerance: true,       // 3 points
      reasonOther: true,           // 1 point
      reasonOtherText: "Other reason",

      // Question 2: Best symptom improvement and no side effects (max: 8 points)
      symptomImprovement: "10",    // 4 points
      sideEffectsNone: true,       // 4 points
      sideEffectsMild: false,
      sideEffectsModerate: false,
      sideEffectsStrong: false,
      sideEffectsDescription: "",

      // Question 3: No health changes (max: 3 points)
      healthChanges: "no-changes", // 3 points
      healthChangesDescription: "",

      // Question 4: Best usage plan (max: 4 points)
      expectations: "Better daily functioning",
      concerns: "None",
      usagePlan: "alternative-situations", // 4 points

      // Question 5: Consent given (max: 5 points)
      consent: "yes"               // 5 points
    },
    expectedScore: 33,
    expectedEligible: true
  },
  {
    name: "Minimum Eligible Score Test",
    description: "Just above the 20% threshold (7 points)",
    formData: {
      // Question 1: Only side effects reason
      reasonSideEffects: true,     // 4 points
      reasonGentlerEffect: false,
      reasonDifferentStrain: false,
      reasonTolerance: false,
      reasonOther: false,
      reasonOtherText: "",

      // Question 2: Low symptom improvement, mild side effects
      symptomImprovement: "3",     // 1 point
      sideEffectsNone: false,
      sideEffectsMild: true,       // 2 points
      sideEffectsModerate: false,
      sideEffectsStrong: false,
      sideEffectsDescription: "",

      // Question 3: Health changes
      healthChanges: "yes",        // 1 point
      healthChangesDescription: "Some changes",

      // Question 4: Other usage plan
      expectations: "Some improvement",
      concerns: "Some concerns",
      usagePlan: "other",          // 1 point

      // Question 5: No consent
      consent: "no"                // 0 points
    },
    expectedScore: 9,
    expectedEligible: true
  },
  {
    name: "Boundary Test - Just Eligible",
    description: "Exactly at the 20% threshold (7 points)",
    formData: {
      // Question 1: Gentler effect + different strain
      reasonSideEffects: false,
      reasonGentlerEffect: true,   // 3 points
      reasonDifferentStrain: true, // 2 points
      reasonTolerance: false,
      reasonOther: false,
      reasonOtherText: "",

      // Question 2: Low scores
      symptomImprovement: "1",     // 0 points
      sideEffectsNone: false,
      sideEffectsMild: false,
      sideEffectsModerate: false,
      sideEffectsStrong: true,     // 0 points
      sideEffectsDescription: "Strong side effects",

      // Question 3: Health changes
      healthChanges: "yes",        // 1 point
      healthChangesDescription: "Some changes",

      // Question 4: Other usage plan
      expectations: "Some improvement",
      concerns: "Some concerns",
      usagePlan: "other",          // 1 point

      // Question 5: No consent
      consent: "no"                // 0 points
    },
    expectedScore: 7,
    expectedEligible: true
  },
  {
    name: "Just Below Threshold Test",
    description: "Just below the 20% threshold (6 points)",
    formData: {
      // Question 1: Only different strain
      reasonSideEffects: false,
      reasonGentlerEffect: false,
      reasonDifferentStrain: true, // 2 points
      reasonTolerance: false,
      reasonOther: false,
      reasonOtherText: "",

      // Question 2: Low scores
      symptomImprovement: "3",     // 1 point
      sideEffectsNone: false,
      sideEffectsMild: false,
      sideEffectsModerate: false,
      sideEffectsStrong: true,     // 0 points
      sideEffectsDescription: "Strong side effects",

      // Question 3: Health changes
      healthChanges: "yes",        // 1 point
      healthChangesDescription: "Some changes",

      // Question 4: Unsure advice
      expectations: "Some improvement",
      concerns: "Some concerns",
      usagePlan: "unsure-advice",  // 2 points

      // Question 5: No consent
      consent: "no"                // 0 points
    },
    expectedScore: 6,
    expectedEligible: false
  },
  {
    name: "Zero Score Test",
    description: "Minimum possible score",
    formData: {
      // Question 1: No reasons selected
      reasonSideEffects: false,    // 0 points
      reasonGentlerEffect: false,  // 0 points
      reasonDifferentStrain: false,// 0 points
      reasonTolerance: false,      // 0 points
      reasonOther: false,          // 0 points
      reasonOtherText: "",

      // Question 2: Worst scores
      symptomImprovement: "1",     // 0 points
      sideEffectsNone: false,
      sideEffectsMild: false,
      sideEffectsModerate: false,
      sideEffectsStrong: true,     // 0 points
      sideEffectsDescription: "Strong side effects",

      // Question 3: Health changes
      healthChanges: "yes",        // 1 point
      healthChangesDescription: "Changes",

      // Question 4: Other usage plan
      expectations: "Some improvement",
      concerns: "Some concerns",
      usagePlan: "other",          // 1 point

      // Question 5: No consent
      consent: "no"                // 0 points
    },
    expectedScore: 2,
    expectedEligible: false
  }
];

// Run tests
console.log("🧪 Running Add 22% THC Scoring Tests...\n");

let passedTests = 0;
let totalTests = testCases.length;

testCases.forEach((testCase, index) => {
  console.log(`Test ${index + 1}: ${testCase.name}`);
  console.log(`Description: ${testCase.description}`);
  
  const result = calculateTotalScore(testCase.formData);
  const isEligible = checkEligibility(result.totalScore);
  
  console.log(`Expected Score: ${testCase.expectedScore}`);
  console.log(`Actual Score: ${result.totalScore}`);
  console.log(`Expected Eligible: ${testCase.expectedEligible}`);
  console.log(`Actual Eligible: ${isEligible}`);
  
  const scoreMatch = result.totalScore === testCase.expectedScore;
  const eligibilityMatch = isEligible === testCase.expectedEligible;
  const testPassed = scoreMatch && eligibilityMatch;
  
  if (testPassed) {
    console.log("✅ PASSED");
    passedTests++;
  } else {
    console.log("❌ FAILED");
    if (!scoreMatch) console.log(`  Score mismatch: expected ${testCase.expectedScore}, got ${result.totalScore}`);
    if (!eligibilityMatch) console.log(`  Eligibility mismatch: expected ${testCase.expectedEligible}, got ${isEligible}`);
  }
  
  console.log("---");
});

// Summary
console.log("\n=== Test Summary ===");
console.log(`Tests Passed: ${passedTests}/${totalTests}`);
console.log(`Success Rate: ${((passedTests/totalTests) * 100).toFixed(1)}%`);

if (passedTests === totalTests) {
  console.log("🎉 All tests passed! The updated 20% eligibility threshold is working correctly.");
} else {
  console.log("⚠️  Some tests failed. Please review the scoring logic.");
}

// Eligibility threshold information
console.log("\n=== Updated Eligibility Information ===");
console.log("Maximum possible score: 33 points");
console.log("New eligibility threshold: 7 points (20% of maximum)");
console.log("Previous threshold: 23 points (70% of maximum)");
console.log("Change impact: Much more lenient - easier for patients to qualify");

// Additional edge case tests
console.log("\n=== Edge Case Tests ===");

// Test individual question scoring
console.log("\n🔍 Individual Question Scoring Verification:");

// Question 1 - Reasons (max 13 points)
const reasonTests = [
  { reasons: ["reasonSideEffects"], expectedScore: 4, description: "Side effects only" },
  { reasons: ["reasonGentlerEffect"], expectedScore: 3, description: "Gentler effect only" },
  { reasons: ["reasonDifferentStrain"], expectedScore: 2, description: "Different strain only" },
  { reasons: ["reasonTolerance"], expectedScore: 3, description: "Tolerance only" },
  { reasons: ["reasonOther"], expectedScore: 1, description: "Other only" },
  { reasons: ["reasonSideEffects", "reasonTolerance"], expectedScore: 7, description: "Side effects + tolerance" },
  { reasons: ["reasonSideEffects", "reasonGentlerEffect", "reasonDifferentStrain", "reasonTolerance", "reasonOther"], expectedScore: 13, description: "All reasons" }
];

reasonTests.forEach(test => {
  let score = 0;
  test.reasons.forEach(reason => {
    score += calculateQuestionScore(reason, true);
  });
  const passed = score === test.expectedScore;
  console.log(`  ${test.description}: ${score}/${test.expectedScore} ${passed ? '✅' : '❌'}`);
});

// Question 2 - Symptom improvement scale (0-4 points)
console.log("\nSymptom Improvement Scale (1-10):");
for (let i = 1; i <= 10; i++) {
  const score = calculateQuestionScore('symptomImprovement', i.toString());
  console.log(`  Rating ${i}: ${score} points`);
}

// Question 2 - Side effects (0-4 points)
console.log("\nSide Effects Scoring:");
const sideEffectTests = [
  { type: "sideEffectsNone", expectedScore: 4, description: "None" },
  { type: "sideEffectsMild", expectedScore: 2, description: "Mild" },
  { type: "sideEffectsModerate", expectedScore: 1, description: "Moderate" },
  { type: "sideEffectsStrong", expectedScore: 0, description: "Strong" }
];

sideEffectTests.forEach(test => {
  const score = calculateQuestionScore(test.type, true);
  const passed = score === test.expectedScore;
  console.log(`  ${test.description}: ${score}/${test.expectedScore} ${passed ? '✅' : '❌'}`);
});

// Question 3 - Health changes (1-3 points)
console.log("\nHealth Changes Scoring:");
const healthTests = [
  { value: "no-changes", expectedScore: 3, description: "No changes" },
  { value: "yes", expectedScore: 1, description: "Yes, changes" }
];

healthTests.forEach(test => {
  const score = calculateQuestionScore('healthChanges', test.value);
  const passed = score === test.expectedScore;
  console.log(`  ${test.description}: ${score}/${test.expectedScore} ${passed ? '✅' : '❌'}`);
});

// Question 4 - Usage plan (1-4 points)
console.log("\nUsage Plan Scoring:");
const usageTests = [
  { value: "alternative-situations", expectedScore: 4, description: "Alternative situations" },
  { value: "rotation-time-symptoms", expectedScore: 3, description: "Rotation based on time/symptoms" },
  { value: "unsure-advice", expectedScore: 2, description: "Unsure, want advice" },
  { value: "other", expectedScore: 1, description: "Other" }
];

usageTests.forEach(test => {
  const score = calculateQuestionScore('usagePlan', test.value);
  const passed = score === test.expectedScore;
  console.log(`  ${test.description}: ${score}/${test.expectedScore} ${passed ? '✅' : '❌'}`);
});

// Question 5 - Consent (0-5 points)
console.log("\nConsent Scoring:");
const consentTests = [
  { value: "yes", expectedScore: 5, description: "Yes" },
  { value: "no", expectedScore: 0, description: "No" }
];

consentTests.forEach(test => {
  const score = calculateQuestionScore('consent', test.value);
  const passed = score === test.expectedScore;
  console.log(`  ${test.description}: ${score}/${test.expectedScore} ${passed ? '✅' : '❌'}`);
});

console.log("\n=== Threshold Analysis ===");
console.log("Scores that would be eligible (≥7 points):");
for (let score = 0; score <= 33; score++) {
  const eligible = checkEligibility(score);
  if (score <= 10 || score >= 30 || (score >= 5 && score <= 9)) {
    console.log(`  Score ${score}: ${eligible ? 'ELIGIBLE ✅' : 'Not eligible ❌'}`);
  }
}

console.log("\n🎯 CONCLUSION: The 20% threshold (7 points) makes the questionnaire much more accessible while still maintaining some basic requirements for eligibility.");
