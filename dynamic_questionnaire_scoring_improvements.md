# Dynamic Questionnaire Scoring Improvements

## Issues Fixed

### 1. **Slider Scoring Not Handled**
**Problem**: The original `calculateScore` method only looked for `answerOptions` but sliders use `sliderConfig.scoreMapping`.

**Solution**: 
- Added separate scoring methods for each question type
- Created `calculateSliderScore()` method that uses `sliderConfig.scoreMapping`
- Updated slider component to use dynamic min/max values from config

### 2. **Checkbox Scoring Logic Flawed**
**Problem**: Checkbox scoring was looking for string values but checkboxes return booleans.

**Solution**:
- Created `calculateCheckboxScore()` method that properly handles boolean values
- Fixed the logic to check `Boolean(value)` instead of string comparison

### 3. **Hardcoded Slider Configuration**
**Problem**: Slider component had hardcoded min=1, max=10 values.

**Solution**:
- Updated slider to use `question.sliderConfig?.min` and `question.sliderConfig?.max`
- Fixed form initialization to use proper slider default values

### 4. **Hardcoded Checkbox Group Title**
**Problem**: Checkbox groups had hardcoded title "Why do you want to add a 22% THC product..."

**Solution**:
- Made checkbox group title dynamic using `currentStepConfig.title`
- Added section description display when available

## Code Changes Made

### 1. QuestionnaireService.ts
```typescript
// NEW: Robust scoring system with type-specific handlers
static calculateScore(formData: Record<string, any>, config: QuestionnaireConfigResponse) {
  // Handle different question types
  switch (question.type) {
    case 'checkbox':
      score = this.calculateCheckboxScore(question, value);
      break;
    case 'radio':
      score = this.calculateRadioScore(question, value);
      break;
    case 'slider':
      score = this.calculateSliderScore(question, value);
      break;
    case 'text':
      score = this.calculateTextScore(question, value);
      break;
  }
}

// NEW: Individual scoring methods for each question type
private static calculateCheckboxScore(question: QuestionConfig, value: any): number {
  const isChecked = Boolean(value);
  const answerOption = question.answerOptions[0];
  return isChecked ? (answerOption?.score || 0) : 0;
}

private static calculateSliderScore(question: QuestionConfig, value: any): number {
  if (!question.sliderConfig?.scoreMapping) return 0;
  const stringValue = String(value || question.sliderConfig.min || 1);
  return question.sliderConfig.scoreMapping[stringValue] || 0;
}
```

### 2. DynamicQuestionnaire.tsx
```typescript
// FIXED: Dynamic slider configuration
case 'slider':
  const sliderMin = question.sliderConfig?.min || 1;
  const sliderMax = question.sliderConfig?.max || 10;
  const defaultValue = Number(value) || sliderMin;
  
  return (
    <Slider
      value={defaultValue}
      min={sliderMin}
      max={sliderMax}
      // ... other props
    />
  );

// FIXED: Dynamic checkbox group title
<FormLabel sx={{ color: "black", fontWeight: "bold" }}>
  {currentStepConfig.title}
</FormLabel>
```

### 3. Types Updated
```typescript
// UPDATED: StepConfig to include section information
export interface StepConfig {
  stepNumber: number;
  title: string;
  questions: QuestionConfig[];
  section?: SectionConfig; // NEW: Include full section info
}
```

## Data Structure Compatibility

The improvements ensure proper handling of your API data structure:

```json
{
  "questions": [
    {
      "key": "symptomImprovement",
      "type": "slider",
      "sliderConfig": {
        "max": 10,
        "min": 1,
        "scoreMapping": {
          "1": 0, "2": 0, "3": 1, "4": 1, "5": 2,
          "6": 2, "7": 3, "8": 3, "9": 4, "10": 4
        }
      },
      "contributesToScore": true
    },
    {
      "key": "reasonSideEffects",
      "type": "checkbox",
      "answerOptions": [
        {
          "label": "Experiencing side effects...",
          "score": 4,
          "value": "reasonSideEffects"
        }
      ],
      "contributesToScore": true
    }
  ]
}
```

## Testing Recommendations

1. **Test Slider Scoring**: Verify that slider values 1-10 map correctly to scores using `scoreMapping`
2. **Test Checkbox Scoring**: Ensure checked checkboxes get proper scores and unchecked ones get 0
3. **Test Radio Scoring**: Confirm radio button selections match the correct `answerOption.score`
4. **Test Mixed Forms**: Verify total scores are calculated correctly across all question types
5. **Test Edge Cases**: 
   - Empty/null values
   - Invalid slider values
   - Missing configuration data

## Benefits

✅ **Accurate Scoring**: All question types now score correctly according to API configuration
✅ **Dynamic Configuration**: No more hardcoded values, everything uses API data
✅ **Better UX**: Proper slider ranges and dynamic titles
✅ **Maintainable**: Type-specific scoring methods are easier to debug and extend
✅ **Robust**: Handles edge cases and missing data gracefully
