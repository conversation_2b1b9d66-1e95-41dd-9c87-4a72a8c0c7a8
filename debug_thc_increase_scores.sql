-- Debug and Fix THC Increase Questionnaire Scores
-- Run these queries step by step to understand and fix the scoring issues

-- =====================================================
-- STEP 1: Check current state of records
-- =====================================================

-- Check how many records we have
SELECT COUNT(*) as total_records FROM thc_increase_questionnaire;

-- Check the structure of questionnaire_data
SELECT 
    id,
    email,
    total_score,
    is_eligible,
    created_at,
    jsonb_pretty(questionnaire_data) as data_structure
FROM thc_increase_questionnaire 
ORDER BY created_at DESC 
LIMIT 1;

-- Check if questionsAndAnswers exists and has scores
SELECT 
    id,
    email,
    total_score,
    jsonb_array_length(questionnaire_data->'questionsAndAnswers') as question_count,
    (questionnaire_data->'questionsAndAnswers'->0) as first_question,
    (questionnaire_data->'questionsAndAnswers'->0->>'score') as first_question_score
FROM thc_increase_questionnaire 
ORDER BY created_at DESC 
LIMIT 5;

-- =====================================================
-- STEP 2: Check for records with zero scores
-- =====================================================

-- Count records with scoring issues
SELECT 
    COUNT(*) as total_records,
    COUNT(CASE WHEN total_score = 0 THEN 1 END) as zero_total_score_records,
    COUNT(CASE WHEN total_score < 10 THEN 1 END) as low_total_score_records,
    COUNT(CASE WHEN (questionnaire_data->'questionsAndAnswers'->0->>'score') IS NULL THEN 1 END) as null_first_score,
    COUNT(CASE WHEN (questionnaire_data->'questionsAndAnswers'->0->>'score')::INTEGER = 0 THEN 1 END) as zero_first_score
FROM thc_increase_questionnaire;

-- =====================================================
-- STEP 3: Simple scoring function for THC Increase
-- =====================================================

-- Create a simplified function to recalculate scores
CREATE OR REPLACE FUNCTION recalculate_thc_increase_score(question_key TEXT, answer_value TEXT)
RETURNS INTEGER AS $$
BEGIN
    CASE question_key
        WHEN 'consistency' THEN
            CASE answer_value
                WHEN 'every-day' THEN RETURN 3;
                WHEN 'most-days' THEN RETURN 2;
                WHEN 'few-times-week' THEN RETURN 1;
                WHEN 'once-week' THEN RETURN 0;
                ELSE RETURN 0;
            END CASE;
        
        WHEN 'dosage' THEN
            CASE answer_value
                WHEN 'less-than-0.5g' THEN RETURN 0;
                WHEN '0.5g-1g' THEN RETURN 1;
                WHEN '1g-2g' THEN RETURN 2;
                WHEN 'more-than-2g' THEN RETURN 4;
                ELSE RETURN 0;
            END CASE;
        
        WHEN 'frequency' THEN
            CASE answer_value
                WHEN 'once-a-day' THEN RETURN 1;
                WHEN 'twice-a-day' THEN RETURN 2;
                WHEN 'three-times-a-day' THEN RETURN 3;
                WHEN 'more-than-three' THEN RETURN 0;
                ELSE RETURN 0;
            END CASE;
        
        WHEN 'condition' THEN
            -- Special scoring: 2 points if not 'other'
            IF answer_value != 'other' AND answer_value IS NOT NULL THEN
                RETURN 2;
            ELSE
                RETURN 0;
            END IF;
        
        WHEN 'effectiveness' THEN
            CASE answer_value
                WHEN '1-2' THEN RETURN 0;
                WHEN '3-4' THEN RETURN 1;
                WHEN '5-6' THEN RETURN 2;
                WHEN '7-8' THEN RETURN 3;
                WHEN '9-10' THEN RETURN 4;
                ELSE RETURN 0;
            END CASE;
        
        WHEN 'symptomChanges' THEN
            CASE answer_value
                WHEN 'significant-improvement' THEN RETURN 4;
                WHEN 'some-improvement' THEN RETURN 3;
                WHEN 'no-change' THEN RETURN 2;
                WHEN 'worsening-symptoms' THEN RETURN 0;
                ELSE RETURN 0;
            END CASE;
        
        WHEN 'sideEffect' THEN
            -- Special scoring for side effects
            IF answer_value = 'none' THEN
                RETURN 4;
            ELSIF answer_value != 'other' AND answer_value IS NOT NULL THEN
                RETURN 1;
            ELSE
                RETURN 0;
            END IF;
        
        WHEN 'sideEffectManageability' THEN
            CASE answer_value
                WHEN '1-2' THEN RETURN 0;
                WHEN '3-4' THEN RETURN 1;
                WHEN '5-6' THEN RETURN 2;
                WHEN '7-8' THEN RETURN 3;
                WHEN '9-10' THEN RETURN 4;
                ELSE RETURN 0;
            END CASE;
        
        WHEN 'concerns' THEN
            CASE answer_value
                WHEN 'no' THEN RETURN 3;
                WHEN 'yes' THEN RETURN 0;
                ELSE RETURN 0;
            END CASE;
        
        WHEN 'treatmentEffectiveness' THEN
            CASE answer_value
                WHEN 'very-effective' THEN RETURN 4;
                WHEN 'somewhat-effective' THEN RETURN 3;
                WHEN 'not-effective' THEN RETURN 0;
                ELSE RETURN 0;
            END CASE;
        
        WHEN 'weaknessAssessment' THEN
            CASE answer_value
                WHEN 'yes-definitely' THEN RETURN 4;
                WHEN 'yes-somewhat' THEN RETURN 3;
                WHEN 'no-satisfied' THEN RETURN 0;
                ELSE RETURN 0;
            END CASE;
        
        WHEN 'insufficientRelief' THEN
            CASE answer_value
                WHEN 'yes-definitely' THEN RETURN 4;
                WHEN 'yes-somewhat' THEN RETURN 3;
                WHEN 'no-adequate' THEN RETURN 0;
                ELSE RETURN 0;
            END CASE;
        
        WHEN 'satisfactionWithForm' THEN
            CASE answer_value
                WHEN 'very-satisfied' THEN RETURN 4;
                WHEN 'somewhat-satisfied' THEN RETURN 3;
                WHEN 'neutral' THEN RETURN 2;
                WHEN 'somewhat-unsatisfied' THEN RETURN 1;
                WHEN 'very-unsatisfied' THEN RETURN 0;
                ELSE RETURN 0;
            END CASE;
        
        WHEN 'openToHigherPotency' THEN
            CASE answer_value
                WHEN 'yes' THEN RETURN 3;
                WHEN 'maybe' THEN RETURN 2;
                WHEN 'no' THEN RETURN 0;
                ELSE RETURN 0;
            END CASE;
        
        WHEN 'quickReliefImportance' THEN
            CASE answer_value
                WHEN 'very-important' THEN RETURN 4;
                WHEN 'somewhat-important' THEN RETURN 3;
                WHEN 'neutral' THEN RETURN 2;
                WHEN 'not-important' THEN RETURN 0;
                ELSE RETURN 0;
            END CASE;
        
        WHEN 'continueTreatment' THEN
            CASE answer_value
                WHEN 'very-likely' THEN RETURN 3;
                WHEN 'somewhat-likely' THEN RETURN 2;
                WHEN 'neutral' THEN RETURN 1;
                WHEN 'unlikely' THEN RETURN 0;
                ELSE RETURN 0;
            END CASE;
        
        WHEN 'overallSatisfaction' THEN
            CASE answer_value
                WHEN 'very-satisfied' THEN RETURN 4;
                WHEN 'somewhat-satisfied' THEN RETURN 3;
                WHEN 'neutral' THEN RETURN 2;
                WHEN 'somewhat-unsatisfied' THEN RETURN 1;
                WHEN 'very-unsatisfied' THEN RETURN 0;
                ELSE RETURN 0;
            END CASE;
        
        ELSE
            RETURN 0;
    END CASE;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- STEP 4: Test the scoring function on one record
-- =====================================================

-- Test scoring calculation on the first record
SELECT 
    id,
    email,
    total_score as current_total_score,
    (
        SELECT SUM(recalculate_thc_increase_score(
            question->>'questionKey', 
            question->>'answerValue'
        ))
        FROM jsonb_array_elements(questionnaire_data->'questionsAndAnswers') AS question
    ) as calculated_total_score
FROM thc_increase_questionnaire 
ORDER BY created_at DESC 
LIMIT 1;

-- =====================================================
-- STEP 5: Show individual question scores for debugging
-- =====================================================

-- Show detailed breakdown for the first record
SELECT 
    id,
    email,
    question->>'questionKey' as question_key,
    question->>'answerValue' as answer_value,
    (question->>'score')::INTEGER as current_score,
    recalculate_thc_increase_score(question->>'questionKey', question->>'answerValue') as calculated_score
FROM thc_increase_questionnaire,
     jsonb_array_elements(questionnaire_data->'questionsAndAnswers') AS question
ORDER BY created_at DESC 
LIMIT 20;
