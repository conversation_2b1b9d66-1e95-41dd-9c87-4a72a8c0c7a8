/**
 * Robust Questionnaire Scoring System
 * Handles all question types: checkbox, radio, slider, text
 */

class QuestionnaireScorer {
  constructor(questionnaireConfig) {
    this.config = questionnaireConfig;
    this.questions = questionnaireConfig.questions || [];
    this.maxScore = questionnaireConfig.maxScore || 0;
    this.threshold = questionnaireConfig.threshold || 0;
  }

  /**
   * Calculate total score from form responses
   * @param {Object} formResponses - User's form responses
   * @returns {Object} - Scoring results
   */
  calculateScore(formResponses) {
    let totalScore = 0;
    const questionScores = {};
    const scoringDetails = [];

    // Process each question that contributes to score
    const scoringQuestions = this.questions.filter(q => q.contributesToScore);

    for (const question of scoringQuestions) {
      const questionScore = this.calculateQuestionScore(question, formResponses);
      
      questionScores[question.key] = questionScore.score;
      totalScore += questionScore.score;
      
      scoringDetails.push({
        questionKey: question.key,
        questionText: question.text,
        questionType: question.type,
        userResponse: questionScore.userResponse,
        score: questionScore.score,
        maxPossibleScore: questionScore.maxPossibleScore,
        scoringMethod: questionScore.scoringMethod
      });
    }

    return {
      totalScore,
      maxScore: this.maxScore,
      threshold: this.threshold,
      isEligible: totalScore >= this.threshold,
      questionScores,
      scoringDetails,
      percentage: this.maxScore > 0 ? Math.round((totalScore / this.maxScore) * 100) : 0
    };
  }

  /**
   * Calculate score for individual question
   * @param {Object} question - Question configuration
   * @param {Object} formResponses - User's form responses
   * @returns {Object} - Question scoring result
   */
  calculateQuestionScore(question, formResponses) {
    const userResponse = formResponses[question.key];
    
    switch (question.type) {
      case 'checkbox':
        return this.scoreCheckboxQuestion(question, userResponse);
      
      case 'radio':
        return this.scoreRadioQuestion(question, userResponse);
      
      case 'slider':
        return this.scoreSliderQuestion(question, userResponse);
      
      case 'text':
        return this.scoreTextQuestion(question, userResponse);
      
      default:
        console.warn(`Unknown question type: ${question.type}`);
        return {
          score: 0,
          maxPossibleScore: 0,
          userResponse: userResponse,
          scoringMethod: 'unknown_type'
        };
    }
  }

  /**
   * Score checkbox questions
   * Checkboxes can be true/false or have multiple options
   */
  scoreCheckboxQuestion(question, userResponse) {
    // Handle boolean checkbox (single option)
    if (question.answerOptions && question.answerOptions.length === 1) {
      const option = question.answerOptions[0];
      const isChecked = Boolean(userResponse);
      
      return {
        score: isChecked ? (option.score || 0) : 0,
        maxPossibleScore: option.score || 0,
        userResponse: isChecked,
        scoringMethod: 'boolean_checkbox'
      };
    }

    // Handle multiple checkbox options (shouldn't happen in current structure, but for robustness)
    if (question.answerOptions && question.answerOptions.length > 1) {
      let score = 0;
      let maxPossible = 0;
      
      question.answerOptions.forEach(option => {
        maxPossible += option.score || 0;
        if (userResponse && userResponse[option.value]) {
          score += option.score || 0;
        }
      });

      return {
        score,
        maxPossibleScore: maxPossible,
        userResponse: userResponse,
        scoringMethod: 'multiple_checkbox'
      };
    }

    return {
      score: 0,
      maxPossibleScore: 0,
      userResponse: userResponse,
      scoringMethod: 'checkbox_no_options'
    };
  }

  /**
   * Score radio button questions
   * Radio buttons have multiple options, user selects one
   */
  scoreRadioQuestion(question, userResponse) {
    if (!question.answerOptions || !userResponse) {
      return {
        score: 0,
        maxPossibleScore: Math.max(...(question.answerOptions || []).map(opt => opt.score || 0)),
        userResponse: userResponse,
        scoringMethod: 'radio_no_selection'
      };
    }

    // Find the selected option
    const selectedOption = question.answerOptions.find(opt => opt.value === userResponse);
    const maxPossible = Math.max(...question.answerOptions.map(opt => opt.score || 0));

    return {
      score: selectedOption ? (selectedOption.score || 0) : 0,
      maxPossibleScore: maxPossible,
      userResponse: userResponse,
      scoringMethod: 'radio_selection'
    };
  }

  /**
   * Score slider questions
   * Sliders use scoreMapping to convert values to scores
   */
  scoreSliderQuestion(question, userResponse) {
    if (!question.sliderConfig || !question.sliderConfig.scoreMapping) {
      return {
        score: 0,
        maxPossibleScore: 0,
        userResponse: userResponse,
        scoringMethod: 'slider_no_mapping'
      };
    }

    const scoreMapping = question.sliderConfig.scoreMapping;
    const responseStr = String(userResponse || question.sliderConfig.min || 1);
    const score = scoreMapping[responseStr] || 0;
    const maxPossible = Math.max(...Object.values(scoreMapping));

    return {
      score,
      maxPossibleScore: maxPossible,
      userResponse: userResponse,
      scoringMethod: 'slider_mapping'
    };
  }

  /**
   * Score text questions
   * Text questions typically don't contribute to score, but included for completeness
   */
  scoreTextQuestion(question, userResponse) {
    // Most text questions don't contribute to score
    // But we might have length-based scoring in some cases
    
    if (question.textScoring) {
      // Custom text scoring logic if defined
      const textLength = (userResponse || '').trim().length;
      
      if (question.textScoring.type === 'length') {
        let score = 0;
        const thresholds = question.textScoring.thresholds || [];
        
        for (const threshold of thresholds) {
          if (textLength >= threshold.minLength) {
            score = threshold.score;
          }
        }
        
        return {
          score,
          maxPossibleScore: question.textScoring.maxScore || 0,
          userResponse: userResponse,
          scoringMethod: 'text_length'
        };
      }
    }

    return {
      score: 0,
      maxPossibleScore: 0,
      userResponse: userResponse,
      scoringMethod: 'text_no_scoring'
    };
  }

  /**
   * Validate form responses before scoring
   * @param {Object} formResponses - User's form responses
   * @returns {Object} - Validation result
   */
  validateResponses(formResponses) {
    const errors = [];
    const warnings = [];

    // Check required questions
    for (const question of this.questions) {
      const response = formResponses[question.key];
      
      // Check if question is required (based on section validation rules or question config)
      const section = this.config.sections?.find(s => s.id === question.sectionId);
      const isRequired = section?.validation_rules?.requireAllQuestions || question.required;
      
      if (isRequired && this.isResponseEmpty(response)) {
        errors.push(`Question "${question.key}" is required but not answered`);
      }

      // Check conditional dependencies
      if (question.dependsOnQuestion && response && !this.isResponseEmpty(response)) {
        const dependencies = question.dependsOnQuestion.split(',');
        const hasValidDependency = dependencies.some(dep => {
          const depResponse = formResponses[dep.trim()];
          return !this.isResponseEmpty(depResponse);
        });

        if (!hasValidDependency) {
          warnings.push(`Question "${question.key}" answered but dependency not met`);
        }
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * Check if a response is considered empty
   */
  isResponseEmpty(response) {
    if (response === null || response === undefined) return true;
    if (typeof response === 'string' && response.trim() === '') return true;
    if (typeof response === 'boolean') return false; // false is a valid response
    if (typeof response === 'number') return false; // 0 is a valid response
    return false;
  }

  /**
   * Get scoring summary for display
   */
  getScoringSummary(scoringResult) {
    return {
      score: `${scoringResult.totalScore}/${scoringResult.maxScore}`,
      percentage: `${scoringResult.percentage}%`,
      status: scoringResult.isEligible ? 'ELIGIBLE' : 'NOT_ELIGIBLE',
      threshold: `${scoringResult.threshold}/${scoringResult.maxScore}`,
      thresholdPercentage: `${Math.round((scoringResult.threshold / scoringResult.maxScore) * 100)}%`
    };
  }
}

// Export for use
if (typeof module !== 'undefined' && module.exports) {
  module.exports = QuestionnaireScorer;
}

// Example usage:
/*
const questionnaireData = {
  // Your questionnaire config data here
};

const scorer = new QuestionnaireScorer(questionnaireData.data);

const formResponses = {
  reasonSideEffects: true,
  reasonGentlerEffect: false,
  symptomImprovement: 7,
  healthChanges: 'no-changes',
  usagePlan: 'alternative-situations',
  consent: 'yes'
  // ... other responses
};

// Validate responses
const validation = scorer.validateResponses(formResponses);
if (!validation.isValid) {
  console.error('Validation errors:', validation.errors);
}

// Calculate score
const result = scorer.calculateScore(formResponses);
console.log('Scoring result:', result);

// Get summary
const summary = scorer.getScoringSummary(result);
console.log('Summary:', summary);
*/
