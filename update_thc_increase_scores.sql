-- Update THC Increase Questionnaire Scores
-- This script fixes individual question scores and recalculates totals

-- =====================================================
-- STEP 1: Update individual question scores in JSONB
-- =====================================================

-- Create function to update scores in questionnaire data
CREATE OR REPLACE FUNCTION update_thc_increase_question_scores(questionnaire_data JSONB)
RETURNS JSONB AS $$
DECLARE
    questions_array JSONB;
    question JSONB;
    updated_questions JSONB := '[]'::JSONB;
    question_key TEXT;
    answer_value TEXT;
    score INTEGER;
BEGIN
    -- Get the questionsAndAnswers array
    questions_array := questionnaire_data->'questionsAndAnswers';
    
    -- If questionsAndAnswers doesn't exist, return original data
    IF questions_array IS NULL THEN
        RETURN questionnaire_data;
    END IF;
    
    -- Loop through each question and recalculate score
    FOR question IN SELECT * FROM jsonb_array_elements(questions_array)
    LOOP
        question_key := question->>'questionKey';
        answer_value := question->>'answerValue';
        score := 0;
        
        -- Calculate score based on question key and answer
        CASE question_key
            WHEN 'consistency' THEN
                CASE answer_value
                    WHEN 'every-day' THEN score := 3;
                    WHEN 'most-days' THEN score := 2;
                    WHEN 'few-times-week' THEN score := 1;
                    WHEN 'once-week' THEN score := 0;
                    ELSE score := 0;
                END CASE;
            
            WHEN 'dosage' THEN
                CASE answer_value
                    WHEN 'less-than-0.5g' THEN score := 0;
                    WHEN '0.5g-1g' THEN score := 1;
                    WHEN '1g-2g' THEN score := 2;
                    WHEN 'more-than-2g' THEN score := 4;
                    ELSE score := 0;
                END CASE;
            
            WHEN 'frequency' THEN
                CASE answer_value
                    WHEN 'once-a-day' THEN score := 1;
                    WHEN 'twice-a-day' THEN score := 2;
                    WHEN 'three-times-a-day' THEN score := 3;
                    WHEN 'more-than-three' THEN score := 0;
                    ELSE score := 0;
                END CASE;
            
            WHEN 'condition' THEN
                -- Special scoring: 2 points if not 'other'
                IF answer_value != 'other' AND answer_value IS NOT NULL AND answer_value != '' THEN
                    score := 2;
                ELSE
                    score := 0;
                END IF;
            
            WHEN 'effectiveness' THEN
                CASE answer_value
                    WHEN '1-2' THEN score := 0;
                    WHEN '3-4' THEN score := 1;
                    WHEN '5-6' THEN score := 2;
                    WHEN '7-8' THEN score := 3;
                    WHEN '9-10' THEN score := 4;
                    ELSE score := 0;
                END CASE;
            
            WHEN 'symptomChanges' THEN
                CASE answer_value
                    WHEN 'significant-improvement' THEN score := 4;
                    WHEN 'some-improvement' THEN score := 3;
                    WHEN 'no-change' THEN score := 2;
                    WHEN 'worsening-symptoms' THEN score := 0;
                    ELSE score := 0;
                END CASE;
            
            WHEN 'sideEffect' THEN
                -- Special scoring for side effects
                IF answer_value = 'none' THEN
                    score := 4;
                ELSIF answer_value != 'other' AND answer_value IS NOT NULL AND answer_value != '' THEN
                    score := 1;
                ELSE
                    score := 0;
                END IF;
            
            WHEN 'sideEffectManageability' THEN
                CASE answer_value
                    WHEN '1-2' THEN score := 0;
                    WHEN '3-4' THEN score := 1;
                    WHEN '5-6' THEN score := 2;
                    WHEN '7-8' THEN score := 3;
                    WHEN '9-10' THEN score := 4;
                    ELSE score := 0;
                END CASE;
            
            WHEN 'concerns' THEN
                CASE answer_value
                    WHEN 'no' THEN score := 3;
                    WHEN 'yes' THEN score := 0;
                    ELSE score := 0;
                END CASE;
            
            WHEN 'treatmentEffectiveness' THEN
                CASE answer_value
                    WHEN 'very-effective' THEN score := 4;
                    WHEN 'somewhat-effective' THEN score := 3;
                    WHEN 'not-effective' THEN score := 0;
                    ELSE score := 0;
                END CASE;
            
            WHEN 'weaknessAssessment' THEN
                CASE answer_value
                    WHEN 'yes-definitely' THEN score := 4;
                    WHEN 'yes-somewhat' THEN score := 3;
                    WHEN 'no-satisfied' THEN score := 0;
                    ELSE score := 0;
                END CASE;
            
            WHEN 'insufficientRelief' THEN
                CASE answer_value
                    WHEN 'yes-definitely' THEN score := 4;
                    WHEN 'yes-somewhat' THEN score := 3;
                    WHEN 'no-adequate' THEN score := 0;
                    ELSE score := 0;
                END CASE;
            
            WHEN 'satisfactionWithForm' THEN
                CASE answer_value
                    WHEN 'very-satisfied' THEN score := 4;
                    WHEN 'somewhat-satisfied' THEN score := 3;
                    WHEN 'neutral' THEN score := 2;
                    WHEN 'somewhat-unsatisfied' THEN score := 1;
                    WHEN 'very-unsatisfied' THEN score := 0;
                    ELSE score := 0;
                END CASE;
            
            WHEN 'openToHigherPotency' THEN
                CASE answer_value
                    WHEN 'yes' THEN score := 3;
                    WHEN 'maybe' THEN score := 2;
                    WHEN 'no' THEN score := 0;
                    ELSE score := 0;
                END CASE;
            
            WHEN 'quickReliefImportance' THEN
                CASE answer_value
                    WHEN 'very-important' THEN score := 4;
                    WHEN 'somewhat-important' THEN score := 3;
                    WHEN 'neutral' THEN score := 2;
                    WHEN 'not-important' THEN score := 0;
                    ELSE score := 0;
                END CASE;
            
            WHEN 'continueTreatment' THEN
                CASE answer_value
                    WHEN 'very-likely' THEN score := 3;
                    WHEN 'somewhat-likely' THEN score := 2;
                    WHEN 'neutral' THEN score := 1;
                    WHEN 'unlikely' THEN score := 0;
                    ELSE score := 0;
                END CASE;
            
            WHEN 'overallSatisfaction' THEN
                CASE answer_value
                    WHEN 'very-satisfied' THEN score := 4;
                    WHEN 'somewhat-satisfied' THEN score := 3;
                    WHEN 'neutral' THEN score := 2;
                    WHEN 'somewhat-unsatisfied' THEN score := 1;
                    WHEN 'very-unsatisfied' THEN score := 0;
                    ELSE score := 0;
                END CASE;
            
            ELSE
                score := 0;
        END CASE;
        
        -- Add the updated question to the array
        updated_questions := updated_questions || jsonb_build_object(
            'questionKey', question->>'questionKey',
            'questionText', question->>'questionText',
            'answerValue', question->>'answerValue',
            'answerText', question->>'answerText',
            'score', score
        );
    END LOOP;
    
    -- Return updated questionnaire data
    RETURN jsonb_set(questionnaire_data, '{questionsAndAnswers}', updated_questions);
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- STEP 2: Update all records with corrected scores
-- =====================================================

-- Update questionnaire data with corrected individual scores
UPDATE thc_increase_questionnaire 
SET 
    questionnaire_data = update_thc_increase_question_scores(questionnaire_data),
    updated_at = NOW();

-- =====================================================
-- STEP 3: Recalculate total scores and eligibility
-- =====================================================

-- Recalculate total scores and eligibility
UPDATE thc_increase_questionnaire 
SET 
    total_score = (
        SELECT COALESCE(SUM((question->>'score')::INTEGER), 0)
        FROM jsonb_array_elements(questionnaire_data->'questionsAndAnswers') AS question
    ),
    is_eligible = (
        SELECT COALESCE(SUM((question->>'score')::INTEGER), 0) >= 45  -- Keep original 45 threshold
        FROM jsonb_array_elements(questionnaire_data->'questionsAndAnswers') AS question
    ),
    updated_at = NOW();

-- =====================================================
-- STEP 4: Verify the updates
-- =====================================================

-- Check the results
SELECT 
    COUNT(*) as total_records,
    COUNT(CASE WHEN total_score > 0 THEN 1 END) as records_with_scores,
    COUNT(CASE WHEN is_eligible = true THEN 1 END) as eligible_records,
    AVG(total_score) as avg_score,
    MIN(total_score) as min_score,
    MAX(total_score) as max_score
FROM thc_increase_questionnaire;

-- Show sample of updated records
SELECT 
    id,
    email,
    total_score,
    is_eligible,
    (questionnaire_data->'questionsAndAnswers'->0->>'score')::INTEGER as first_question_score,
    updated_at
FROM thc_increase_questionnaire 
ORDER BY updated_at DESC 
LIMIT 5;
