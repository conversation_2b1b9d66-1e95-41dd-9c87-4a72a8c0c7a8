import React, { useState, useEffect } from "react";
import {
  <PERSON>,
  Typo<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>ack,
  Al<PERSON>,
  CircularProgress,
  <PERSON>rid2 as <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@mui/material";
import { ArrowBack } from "@mui/icons-material";
import MenuIcon from "@mui/icons-material/Menu";
import CloseIcon from "@mui/icons-material/Close";
import { useNavigate } from "@tanstack/react-location";
import { useSnackbar } from "notistack";
import { useFlow } from "../../../hooks/flow-controller";
import { checkAdd22ThcEligibility } from "../../../utils/treatmentPlanValidation";
import axiosInstance from "../../../services/axios";
import DynamicQuestionnaire from "../common/DynamicQuestionnaire";
import Banner from "../layouts/Banner";
import zenithTheme from "../../../styles/zenith/theme";
import CachedImage from "../common/CachedImage";

const Add22ThcDynamic: React.FC = () => {
  const navigate = useNavigate();
  const { enqueueSnackbar } = useSnackbar();
  const { user } = useFlow();

  // Component state
  const [isLoading, setIsLoading] = useState(false);
  const [canProceed, setCanProceed] = useState(false);
  const [contactId, setContactId] = useState<string>("");
  const [currentStep, setCurrentStep] = useState(1);
  const [totalSteps, setTotalSteps] = useState(5);
  const [maxScore, setMaxScore] = useState<number>(33); // Default fallback, will be updated from API

  // Mobile drawer state
  const [drawerOpen, setDrawerOpen] = useState(false);

  // Check eligibility on component mount
  useEffect(() => {
    const checkEligibility = async () => {
      if (!user?.email) {
        enqueueSnackbar("Please log in to access this questionnaire", { variant: "error" });
        navigate({ to: "/patient/login" });
        return;
      }

      try {
        setIsLoading(true);
        
        // Fetch treatment plan data
        const treatmentResponse = await axiosInstance.get(
          `/zoho/v1.0/treatment-plan/by-email?email=${encodeURIComponent(user.email)}`
        );

        if (treatmentResponse.data?.success && treatmentResponse.data?.treatmentPlan) {
          const planData = treatmentResponse.data.treatmentPlan;
          setContactId(treatmentResponse.data.contactId || "");

          // Check eligibility for 22% THC questionnaire
          const eligibilityResult = checkAdd22ThcEligibility(planData);
          
          if (!eligibilityResult.isEligible) {
            enqueueSnackbar(eligibilityResult.reason, { variant: "error" });
            navigate({ to: "/patient/home" });
            return;
          }

          setCanProceed(true);
        } else {
          enqueueSnackbar("Unable to fetch treatment plan data", { variant: "error" });
          navigate({ to: "/patient/home" });
        }
      } catch (error) {
        console.error("Error checking eligibility:", error);
        enqueueSnackbar("Error checking eligibility. Please try again.", { variant: "error" });
        navigate({ to: "/patient/home" });
      } finally {
        setIsLoading(false);
      }
    };

    checkEligibility();
  }, [user?.email, navigate, enqueueSnackbar]);

  // Handle score changes from the dynamic questionnaire
  const handleScoreChange = (totalScore: number, apiMaxScore: number, isEligible: boolean) => {
    // Update maxScore from API configuration to ensure consistency
    setMaxScore(apiMaxScore);
  };

  // Handle step changes for progress indicator
  const handleStepChange = (step: number, total: number) => {
    setCurrentStep(step);
    setTotalSteps(total);
  };

  // Handle questionnaire submission
  const handleSubmit = async (submissionData: any) => {
    try {
      setIsLoading(true);

      // Transform dynamic questionnaire data to match original endpoint format
      const transformedData = {
        questionsAndAnswers: submissionData.responses, // rename 'responses' to 'questionsAndAnswers'
        totalScore: submissionData.totalScore,
        maxScore: maxScore, // Use dynamic maxScore from API configuration
        isEligible: submissionData.isEligible,
        submittedAt: submissionData.submittedAt,
      };

      // Submit to the backend using the same endpoint as the original Add22Thc component
      const result = await axiosInstance.post(
        `${import.meta.env.VITE_API_URL}/funnel/v1.0/patient/add-22-thc-questionnaire`,
        transformedData,
        { withCredentials: true }
      );

      if (result.data) {
        // Show success message (same as original implementation)
        enqueueSnackbar("Congratulations! Your request to add 22% THC is under review.", {
          variant: "success",
        });

        // Navigate back to home page to show approval/rejection status
        navigate({ to: "/patient/home" });
      }
    } catch (error) {
      console.error("Error submitting questionnaire:", error);
      enqueueSnackbar("Failed to submit questionnaire. Please try again.", { variant: "error" });
    } finally {
      setIsLoading(false);
    }
  };

  // Handle back navigation
  const handleBack = () => {
    navigate({ to: "/patient/home" });
  };

  // Mobile drawer handlers
  const handleDrawerToggle = () => {
    setDrawerOpen(!drawerOpen);
  };

  const handleLogout = () => {
    // Clear any stored authentication data
    localStorage.removeItem('authToken');
    sessionStorage.clear();

    // Navigate to login page
    navigate({ to: "/patient/login" });
  };



  if (isLoading && !canProceed) {
    return (
      <ThemeProvider theme={zenithTheme}>
        <Box sx={{ minHeight: "100vh", bgcolor: "background.default" }}>
          <Banner />
          <Box sx={{ display: "flex", justifyContent: "center", alignItems: "center", minHeight: "50vh" }}>
            <CircularProgress />
          </Box>
        </Box>
      </ThemeProvider>
    );
  }

  if (!canProceed) {
    return (
      <ThemeProvider theme={zenithTheme}>
        <Box sx={{ minHeight: "100vh", bgcolor: "background.default" }}>
          <Banner />
          <Box sx={{ p: 3 }}>
            <Alert severity="error">
              You are not eligible for this questionnaire. Please contact your doctor for more information.
            </Alert>
          </Box>
        </Box>
      </ThemeProvider>
    );
  }

  return (
    <ThemeProvider theme={zenithTheme}>
      <Box sx={{ minHeight: "100vh", bgcolor: "background.default", display: "flex", flexDirection: "column" }}>
        {/* Custom Header */}
        <Box sx={{ width: "100%" }}>
          <Banner />

          {/* Nav Bar */}
          <Box
            sx={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              padding: "0 20px",
              backgroundColor: "white",
              // Account for banner height + some spacing
              paddingBottom: "10px",
            }}
          >
            <IconButton aria-label="menu" sx={{ padding: "8px" }} onClick={handleDrawerToggle}>
              <MenuIcon />
            </IconButton>

            <CachedImage
              src="/zenith/zenith-logo.png"
              alt="Zenith Clinics"
              sx={{
                height: "19.14px",
              }}
            />
          </Box>
        </Box>

        {/* Mobile Drawer */}
        <Drawer
          anchor="top"
          open={drawerOpen}
          onClose={handleDrawerToggle}
          keepMounted={true}
          sx={{
            "& .MuiDrawer-paper": {
              width: "100%",
              maxWidth: "100%",
              boxSizing: "border-box",
              padding: "20px 0 0 0",
              height: "auto",
              maxHeight: "500px",
              overflowY: "auto",
              borderBottom: "1px solid #e0e0e0",
              top: { xs: "40px", sm: "48px" }, // Push down by banner height
              zIndex: 1299, // Just below the banner
            },
          }}
        >
          {/* Drawer Header */}
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', padding: '0 20px', marginBottom: '10px' }}>
            <CachedImage
              src="/zenith/zenith-logo.png"
              alt="Zenith Clinics"
              sx={{ height: '36px' }}
            />
            <IconButton onClick={handleDrawerToggle}>
              <CloseIcon />
            </IconButton>
          </Box>

          {/* Drawer Content */}
          <Box sx={{ display: 'flex', flexDirection: 'column', padding: '0 20px' }}>
            <Button
              onClick={() => {
                navigate({ to: "/patient/home" });
                setDrawerOpen(false);
              }}
              sx={{
                color: '#217F00',
                fontSize: '18px',
                fontWeight: 'bold',
                justifyContent: 'flex-start',
                padding: '15px 0',
                width: '100%',
                textTransform: 'none',
                borderBottom: '1px solid #f0f0f0'
              }}
            >
              Home
            </Button>
            <Button
              onClick={() => {
                window.open(import.meta.env.VITE_ZENITH_PRIVATE_SHOP_URL, '_blank');
                setDrawerOpen(false);
              }}
              sx={{
                color: '#217F00',
                fontSize: '18px',
                fontWeight: 'bold',
                justifyContent: 'flex-start',
                padding: '15px 0',
                width: '100%',
                textTransform: 'none',
                borderBottom: '1px solid #f0f0f0'
              }}
            >
              Visit Private Shop
            </Button>
            <Button
              onClick={handleLogout}
              sx={{
                color: '#217F00',
                fontSize: '18px',
                fontWeight: 'bold',
                justifyContent: 'flex-start',
                padding: '15px 0',
                width: '100%',
                textTransform: 'none'
              }}
            >
              Logout
            </Button>
          </Box>
        </Drawer>

        {/* Main Content - Exact layout from original Add22Thc.tsx */}
        <Box sx={{ flex: 1, padding: "20px" }}>
          {/* Back Button */}
          <Box sx={{ marginBottom: 2, display: "flex", justifyContent: "flex-start", maxWidth: "800px", margin: "0 auto 16px auto", width: "100%" }}>
            <Button
              variant="outlined"
              startIcon={<ArrowBack />}
              onClick={handleBack}
              sx={{
                color: "#217F00",
                borderColor: "#217F00",
                fontSize: "16px",
                fontWeight: "bold",
                textTransform: "none",
                padding: "8px 16px",
                "&:hover": {
                  backgroundColor: "rgba(33, 127, 0, 0.04)",
                  borderColor: "#217F00",
                },
              }}
            >
              Back to Home
            </Button>
          </Box>

          <Grid
            container
            direction="column"
            alignItems="center"
            justifyContent="flex-start"
            spacing={2}
          >
            <Grid size={12}>
              <Stack direction="row" alignItems="center" justifyContent="center">
                <Box
                  sx={{
                    width: "32px",
                    borderRadius: "15px 0 0 15px",
                    height: "11px",
                    backgroundColor: currentStep >= 1 ? "green" : "#EAEAEA",
                    border: "0.5px solid rgba(89, 89, 89, 0.61)",
                  }}
                />
                {[...Array(totalSteps - 2)].map((_, index) => (
                  <Box
                    key={index}
                    sx={{
                      width: "32px",
                      height: "11px",
                      backgroundColor: currentStep > index + 1 ? "green" : "#EAEAEA",
                      border: "0.5px solid rgba(89, 89, 89, 0.61)",
                    }}
                  />
                ))}
                <Box
                  sx={{
                    width: "32px",
                    borderRadius: "0 15px 15px 0",
                    height: "11px",
                    backgroundColor: currentStep >= totalSteps ? "green" : "#EAEAEA",
                    border: "0.5px solid rgba(89, 89, 89, 0.61)",
                  }}
                />
              </Stack>
            </Grid>
            <Grid size={12}>
              <Stack>
                <Typography variant="h4" fontWeight={800}>
                  Questionnaire:
                </Typography>
                <Typography variant="h5" fontWeight={700} color="green">
                  Add 22% THC Option
                </Typography>
              </Stack>
            </Grid>

            {/* Dynamic Questionnaire */}
            <DynamicQuestionnaire
              questionnaireType="add_22_thc"
              patientId={contactId}
              onSubmit={handleSubmit}
              onScoreChange={handleScoreChange}
              onStepChange={handleStepChange}
            />
          </Grid>
        </Box>
      </Box>
    </ThemeProvider>
  );
};

export default Add22ThcDynamic;
