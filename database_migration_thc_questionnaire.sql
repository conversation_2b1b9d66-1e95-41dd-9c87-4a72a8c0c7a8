-- Migration: Create THC Increase Questionnaire Table
-- This table stores T<PERSON> increase questionnaire submissions with JSONB for flexible data storage

CREATE TABLE IF NOT EXISTS thc_increase_questionnaire (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),

    -- Patient identification (following existing patterns)
    patient_id VARCHAR REFERENCES patient("patientID"),
    email TEXT NOT NULL,
    zoho_id TEXT,

    -- Questionnaire data stored as JSONB for flexibility
    questionnaire_data JSONB NOT NULL,

    -- Scoring information
    total_score INTEGER NOT NULL DEFAULT 0,
    max_score INTEGER NOT NULL DEFAULT 61,
    is_eligible BOOLEAN NOT NULL DEFAULT FALSE,

    -- Status tracking
    status TEXT NOT NULL DEFAULT 'submitted', -- submitted, under_review, approved, rejected
    reviewed_by TEXT, -- doctor ID who reviewed
    reviewed_at TIMESTAMP WITH TIME ZONE,
    review_notes TEXT,

    -- Approval tracking
    approved_by TEXT, -- doctor ID who approved
    approved_at TIMESTAMP WITH TIME ZONE,
    approval_notes TEXT,

    -- Submission metadata
    ip_address TEXT,
    user_agent TEXT,

    -- Timestamps (following existing patterns)
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_thc_questionnaire_patient_id ON thc_increase_questionnaire(patient_id);
CREATE INDEX IF NOT EXISTS idx_thc_questionnaire_email ON thc_increase_questionnaire(email);
CREATE INDEX IF NOT EXISTS idx_thc_questionnaire_status ON thc_increase_questionnaire(status);
CREATE INDEX IF NOT EXISTS idx_thc_questionnaire_is_eligible ON thc_increase_questionnaire(is_eligible);
CREATE INDEX IF NOT EXISTS idx_thc_questionnaire_created_at ON thc_increase_questionnaire(created_at);

-- Create GIN index for JSONB data for efficient querying
CREATE INDEX IF NOT EXISTS idx_thc_questionnaire_data_gin ON thc_increase_questionnaire USING GIN (questionnaire_data);

-- Add trigger to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION update_thc_questionnaire_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_thc_questionnaire_updated_at
    BEFORE UPDATE ON thc_increase_questionnaire
    FOR EACH ROW
    EXECUTE FUNCTION update_thc_questionnaire_updated_at();

-- Add comments for documentation
COMMENT ON TABLE thc_increase_questionnaire IS 'Stores THC increase questionnaire submissions with scoring and approval workflow';
COMMENT ON COLUMN thc_increase_questionnaire.questionnaire_data IS 'JSONB containing all questionnaire answers and metadata';
COMMENT ON COLUMN thc_increase_questionnaire.total_score IS 'Calculated total score from questionnaire answers (0-61)';
COMMENT ON COLUMN thc_increase_questionnaire.is_eligible IS 'Whether patient is eligible for 29% THC treatment (score >= 45)';
COMMENT ON COLUMN thc_increase_questionnaire.status IS 'Current status: submitted, under_review, approved, rejected';
COMMENT ON COLUMN thc_increase_questionnaire.ip_address IS 'IP address of the user who submitted the questionnaire';
COMMENT ON COLUMN thc_increase_questionnaire.user_agent IS 'User agent string of the browser used to submit the questionnaire';

-- Example of the JSONB structure that will be stored:
/*
questionnaire_data JSONB structure:
{
  "questionsAndAnswers": [
    {
      "questionKey": "consistency",
      "questionText": "How often did you use 22% THC flower?",
      "answerValue": "every-day",
      "answerText": "Every day",
      "score": 3
    },
    {
      "questionKey": "dosage",
      "questionText": "What was your typical dosage per session?",
      "answerValue": "more-than-2g",
      "answerText": "More than 2g",
      "score": 4
    },
    {
      "questionKey": "frequency",
      "questionText": "How many times per day did you typically use it?",
      "answerValue": "three-times-a-day",
      "answerText": "Three times a day",
      "score": 3
    },
    {
      "questionKey": "condition",
      "questionText": "What condition are you using THC flower to treat?",
      "answerValue": "chronic-pain",
      "answerText": "Chronic pain",
      "score": 2
    },
    {
      "questionKey": "effectiveness",
      "questionText": "On a scale of 1 to 10, how effective has 22% THC flower been in managing your symptoms?",
      "answerValue": "9-10",
      "answerText": "9-10 (Very effective)",
      "score": 4
    }
  ],

  "scoring": {
    "questionScores": {
      "consistency": 3,
      "dosage": 4,
      "frequency": 3,
      "condition": 2,
      "effectiveness": 4,
      "symptomChanges": 4,
      "sideEffect": 4,
      "sideEffectManageability": 4,
      "concerns": 3,
      "treatmentEffectiveness": 4,
      "weaknessAssessment": 4,
      "insufficientRelief": 4,
      "satisfactionWithForm": 4,
      "openToHigherPotency": 3,
      "quickReliefImportance": 4,
      "continueTreatment": 3,
      "overallSatisfaction": 4
    }
  },
  "metadata": {
    "submittedAt": "2025-01-07T10:30:00Z",
    "ipAddress": "***********",
    "userAgent": "Mozilla/5.0...",
    "completionTimeMinutes": 8
  }
}
*/
