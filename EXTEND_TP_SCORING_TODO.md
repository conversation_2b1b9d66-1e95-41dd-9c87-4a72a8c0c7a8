# ExtendTP Questionnaire Scoring Implementation TODO

## Analysis Summary

### Current State ❌ INSUFFICIENT
- The ExtendTP.tsx component has minimal implementation with only 3 basic questions
- **CRITICAL ISSUE**: Current 3 questions can only provide ~15 points maximum
- **REQUIRED**: 42+ points needed for eligibility (missing 27+ points)
- Currently no form state management or data collection
- No scoring system implemented
- No API submission logic
- Questions are present but answers are not being captured

### Scoring Model Requirements ✅ CONFIRMED SUFFICIENT
- Total possible score: 60 points (12 questions × 5 points each)
- Eligibility threshold: 42+ points (70% of total) for 6-month treatment plan eligibility
- 12 questions with varying point values (0-5 points each)
- **IMPORTANT**: Users select ONE option per question (no multi-select)
- **CONFIRMED**: With all 12 questions, patients have multiple pathways to reach 42+ points

## Implementation Plan

### **CRITICAL REQUIREMENT**: Complete Questionnaire Replacement
- **Current 3 questions MUST be replaced** with all 12 ExtendTP questions
- **Reason**: Current maximum ~15 points vs required 42+ points for eligibility
- **Solution**: Implement complete 12-question clinical assessment framework

### Phase 1: Form State Management
- [ ] **Replace existing questions** - Remove current 3 inadequate questions
- [ ] **Add form data state** - Create comprehensive state object to store all 12 answers
- [ ] **Add scoring state** - Track current total score and individual question scores
- [ ] **Add form validation** - Ensure all questions are answered before proceeding
- [ ] **Add answer handlers** - Implement onChange handlers for all radio groups

### Phase 2: Scoring Logic Implementation
- [ ] **Create scoring mapping object** - Map each answer value to its point value (0-5 points each)
- [ ] **Implement score calculation function** - Calculate total score based on answers
- [ ] **Add real-time scoring** - Update score as user answers questions
- [ ] **Add eligibility determination** - Check if score >= 42 for 6-month plan eligibility

### Phase 3: Question Implementation (12 Questions Total)
Based on the provided scoring model, here are the questions that need to be implemented:

#### Question 1: Adherence
**Text**: "During the past 3 months, how often did you use your medicinal cannabis exactly as prescribed (correct dose and timing)?"
**Options**:
- Always followed the prescribed schedule (5 points)
- Usually followed the schedule, with only a few minor misses or adjustments (4 points)
- Sometimes followed the schedule, with occasional missed or extra doses (2 points)
- Rarely followed the prescribed schedule (1 point)
- Never followed the prescribed regimen (0 points)

#### Question 2: Symptom Improvement
**Text**: "How much has your primary symptom (the main condition you are treating, e.g. pain, anxiety, insomnia) improved since starting medicinal cannabis?"
**Options**:
- Significant improvement: symptoms have greatly reduced (5 points)
- Moderate improvement: noticeable improvement, but symptoms are still present (4 points)
- Slight improvement: only a small improvement in symptoms (2 points)
- No improvement: no change in the symptoms (1 point)
- Symptoms worsened: symptoms have become worse than before (0 points)

#### Question 3: Symptom Frequency
**Text**: "How has the frequency or occurrence of your symptoms changed with treatment? (For example, how often you experience pain flare-ups, anxiety attacks, or sleepless nights now versus before.)"
**Options**:
- Much less often than before: symptoms rarely occur now (5 points)
- Somewhat less often than before: symptoms occur less frequently (4 points)
- About the same as before: no real change in how often symptoms occur (2 points)
- Somewhat more often than before: symptoms occur a bit more frequently (1 point)
- Much more often than before: symptoms occur far more frequently (0 points)

#### Question 4: Use of Additional Relief
**Text**: "Did you need to use any extra treatments besides the prescribed cannabis to manage your condition (such as additional medications or extra cannabis doses)?"
**Options**:
- No, none: cannabis alone was sufficient to manage my symptoms (5 points)
- Rarely: only on a few occasions I needed something additional (4 points)
- Sometimes: I sometimes took another medication or extra dose for better relief (2 points)
- Frequently: I often needed other medications or extra doses to control my symptoms (0 points)

#### Question 5: Functional Benefit
**Text**: "How has the treatment affected your daily functioning or quality of life (ability to perform work, household tasks, exercise, socialize, etc.)?"
**Options**:
- Significantly improved: I'm much more able to carry out daily activities than before (5 points)
- Somewhat improved: I can do more daily activities than before, though still with some difficulty (4 points)
- No change: my ability to perform daily tasks is about the same as before (2 points)
- Somewhat worsened: day-to-day activities have become a bit more difficult (1 point)
- Significantly worsened: I'm much less able to function in daily life than before (0 points)

#### Question 6: Sleep Quality
**Text**: "How has your sleep quality or pattern been affected by the treatment?"
**Options**:
- Much improved: I am sleeping significantly better than before (5 points)
- Somewhat improved: I'm sleeping a bit better than before (4 points)
- No change: my sleep is neither better nor worse than before (2 points)
- Somewhat worse: I have experienced slightly worse sleep or new sleep issues (1 point)
- Much worse: my sleep has significantly worsened since starting treatment (0 points)

#### Question 7: Tolerance
**Text**: "Did you find that you needed to increase your cannabis dose over time to get the same symptom relief?"
**Options**:
- No increase needed: I did not need to raise the dose; the same amount worked throughout (5 points)
- Yes, slight increase: I needed a small increase in dose/frequency to maintain relief (4 points)
- Yes, significant increase: I had to greatly increase the dose or use much more often for the same effect (2 points)
- Yes, and effect decreased: even with higher doses, the medication felt less effective than before (0 points)

#### Question 8: Side Effect Severity
**Text**: "Which statement best describes the side effects you experienced from the medicinal cannabis?"
**Options**:
- None or very mild side effects: I had no noticeable side effects (5 points)
- Mild side effects: e.g. slight dry mouth or mild drowsiness that did not bother me (4 points)
- Moderate side effects: e.g. dizziness, increased appetite, or some anxiety – noticeable but manageable (2 points)
- Severe side effects: e.g. very strong unwanted effects (such as severe dizziness, confusion, vomiting, etc.) that were hard to tolerate (0 points)

#### Question 9: Side Effect Tolerability
**Text**: "How did any side effects impact your willingness to continue treatment?"
**Options**:
- Not at all: side effects did not affect my willingness to continue using the medicine (5 points)
- A little: side effects were somewhat bothersome but I felt I could manage and continue (4 points)
- Moderately: side effects made me question whether to continue at times (2 points)
- Severely: side effects were intolerable – I felt I needed to stop treatment because of them (0 points)

#### Question 10: Overall Satisfaction
**Text**: "Overall, how satisfied are you with the results of your medicinal cannabis treatment so far?"
**Options**:
- Very satisfied: extremely pleased with the treatment's results (5 points)
- Somewhat satisfied: mostly happy, with a few minor concerns (4 points)
- Neutral: neither satisfied nor dissatisfied with the results (2 points)
- Somewhat dissatisfied: somewhat unhappy; results fell short of expectations (1 point)
- Very dissatisfied: very unhappy; treatment did not help as hoped (0 points)

#### Question 11: Goal Achievement
**Text**: "To what extent has this treatment met the goals or expectations you had when you started?"
**Options**:
- Completely met: it fully met or even exceeded my treatment goals (5 points)
- Mostly met: it met most of my main goals, with only slight shortfalls (4 points)
- Partially met: it met some of my goals but not others (2 points)
- Not met: it did not meet my expectations or goals at all (0 points)

#### Question 12: Treatment Intent
**Text**: "What would you like to do going forward after this 3-month trial?"
**Options**:
- Continue the current treatment into a 6-month plan (I wish to keep using medicinal cannabis as is) – eligible for telehealth follow-up (5 points)
- Continue with adjustments (I want to continue, but perhaps with some changes in dose/strain) – eligible for telehealth follow-up (4 points)
- Unsure (I'm not certain if I should continue and would like to discuss it more) (2 points)
- Stop the treatment (I prefer not to continue with medicinal cannabis) (0 points)

### Phase 4: UI Structure & Components (Following ThcIncrease Pattern)
- [ ] **Implement multi-step form** - Create 12 steps (group into logical sections like ThcIncrease)
- [ ] **Add progress bar** - Visual progress indicator with green boxes (copy from ThcIncrease)
- [ ] **Add step navigation** - Forward/backward navigation with validation
- [ ] **Add validation feedback** - Show which questions need answers with error messages
- [ ] **Add loading states** - Button opacity, loading text, disabled states
- [ ] **Add results display** - Show final score and eligibility determination

### Phase 5: Form Validation & User Experience (Following ThcIncrease Pattern)
- [ ] **Step-by-step validation** - Validate each step before proceeding
- [ ] **Real-time canProceed state** - Update button state based on current step completion
- [ ] **Error messaging** - "Please answer all questions to continue" feedback
- [ ] **Button states** - Continue/Submit button with proper disabled states
- [ ] **Loading feedback** - "Submitting..." text during API calls

### Phase 6: Data Persistence & API Integration (Following ThcIncrease Pattern)
- [ ] **Create TypeScript interfaces** - ExtendTPFormData and ScoringState interfaces
- [ ] **Add API submission** - POST to `/funnel/v1.0/patient/extend-tp-questionnaire`
- [ ] **Add error handling** - Try/catch with proper error messages
- [ ] **Add loading states** - isLoading state management
- [ ] **Add success feedback** - Success/eligibility snackbar messages
- [ ] **Add navigation** - Redirect to `/patient/home` after submission

### Phase 7: Question & Answer Text Mapping (Following ThcIncrease Pattern)
- [ ] **Create getQuestionText function** - Map question keys to display text
- [ ] **Create getAnswerText function** - Map answer values to display text
- [ ] **Add questionsAndAnswers array** - For API submission and display
- [ ] **Add proper text formatting** - Consistent question and answer formatting

## Technical Implementation Details (Following ThcIncrease Pattern)

### Required Imports (Copy from ThcIncrease):
```typescript
import { useState, useEffect } from "react";
import {
  Box, Button, Typography, TextField, Stack, FormControl,
  FormLabel, RadioGroup, FormControlLabel, Radio, Divider,
} from "@mui/material";
import Grid from "@mui/material/Grid2";
import axios from "axios";
import { useSnackbar } from "notistack";
import { useNavigate } from "@tanstack/react-location";
```

### State Structure (Following ThcIncrease Pattern):
```typescript
interface ExtendTPFormData {
  adherence: string;
  symptomImprovement: string;
  symptomFrequency: string;
  additionalRelief: string;
  functionalBenefit: string;
  sleepQuality: string;
  tolerance: string;
  sideEffectSeverity: string;
  sideEffectTolerability: string;
  overallSatisfaction: string;
  goalAchievement: string;
  treatmentIntent: string;
}

interface ScoringState {
  totalScore: number;
  maxScore: number; // 60 for ExtendTP
  isEligible: boolean;
  questionScores: Record<string, number>;
}
```

### Required State Variables (Copy from ThcIncrease):
```typescript
const [step, setStep] = useState(1);
const [isLoading, setIsLoading] = useState(false);
const { enqueueSnackbar } = useSnackbar();
const navigate = useNavigate();
const [formData, setFormData] = useState<ExtendTPFormData>({...});
const [scoring, setScoring] = useState<ScoringState>({
  totalScore: 0,
  maxScore: 60, // 12 questions × 5 points each
  isEligible: false,
  questionScores: {},
});
const [canProceed, setCanProceed] = useState(false);
```

### Key Functions to Implement (Copy Pattern from ThcIncrease):
- `handleRadioChange(questionKey: keyof ExtendTPFormData, value: string): void`
- `calculateQuestionScore(questionKey: keyof ExtendTPFormData, answer: string): number`
- `calculateTotalScore(data: ExtendTPFormData): number`
- `checkEligibility(score: number): boolean` - return score >= 42
- `validateCurrentStep(): boolean` - validate current step completion
- `getQuestionText(questionKey: string): string` - question display mapping
- `getAnswerText(questionKey: string, answerValue: string): string` - answer display mapping
- `submitQuestionnaire(): Promise<void>` - API submission with error handling

## Scoring Summary ✅ CONFIRMED ADEQUATE
- **Total Maximum Score**: 60 points (12 questions × 5 points each)
- **Eligibility Threshold**: 42+ points (70% of total)
- **Current Implementation**: Only ~15 points possible (INSUFFICIENT)
- **Required Implementation**: All 12 questions for 60 points possible (SUFFICIENT)
- **Outcome**: Score ≥ 42 = Eligible for 6-month treatment plan via telehealth
- **Outcome**: Score < 42 = Requires face-to-face consultation before continuing

### **Score Achievement Examples:**
- **High performer**: 8×5pts + 4×4pts = 56 points ✅ ELIGIBLE
- **Good performer**: 4×5pts + 6×4pts + 2×2pts = 48 points ✅ ELIGIBLE
- **Moderate performer**: 2×5pts + 4×4pts + 4×2pts + 2×1pts = 36 points ❌ NOT ELIGIBLE
- **Minimum to pass**: 6×5pts + 3×4pts + 3×0pts = 42 points ✅ EXACTLY ELIGIBLE

## Implementation Priority
1. **CRITICAL PRIORITY**: Replace all 3 current questions with 12 proper ExtendTP questions
2. **High Priority**: Form state management and scoring logic (0-60 point system)
3. **Medium Priority**: UI enhancements and validation
4. **Low Priority**: API integration and advanced features

### Critical Implementation Details from ThcIncrease:

#### 1. **Scoring Map Structure** (Copy Pattern):
```typescript
const scoringMap: Record<string, Record<string, number>> = {
  adherence: {
    'always-followed': 5,
    'usually-followed': 4,
    'sometimes-followed': 2,
    'rarely-followed': 1,
    'never-followed': 0
  },
  // ... repeat for all 12 questions
};
```

#### 2. **useEffect Hooks** (Copy from ThcIncrease):
- Real-time scoring update: `useEffect(() => { calculateTotalScore(); }, [formData])`
- Step validation: `useEffect(() => { setCanProceed(validateCurrentStep()); }, [step, formData])`

#### 3. **Progress Bar** (Copy exact structure from ThcIncrease):
- Green progress boxes with rounded corners
- Dynamic step highlighting based on current step
- Array mapping for visual progress indication

#### 4. **API Submission Structure** (Copy from ThcIncrease):
```typescript
const submissionData = {
  questionsAndAnswers,
  totalScore: scoring.totalScore,
  maxScore: scoring.maxScore,
  isEligible: scoring.isEligible,
  submittedAt: new Date().toISOString(),
};
```

#### 5. **Button States & Loading** (Copy from ThcIncrease):
- Opacity changes based on canProceed state
- Disabled state management
- Loading text: "Submitting..." vs "Continue"/"Submit"
- Error message display below button

#### 6. **Step Structure** (Follow ThcIncrease Pattern):
- Individual step functions: `stepOne()`, `stepTwo()`, etc.
- `renderStep()` switch statement
- Consistent FormControl, FormLabel, RadioGroup structure
- Divider components between questions

#### 7. **Navigation & Success Handling** (Copy from ThcIncrease):
- Success snackbar with score display
- Eligibility-based messaging
- Navigation to `/patient/home` after submission
- Error handling with try/catch blocks

## Notes
- **MUST completely replace current inadequate questions**
- **COPY EXACT PATTERNS** from ThcIncrease.tsx implementation
- Use identical state management approach and component structure
- Maintain existing UI/UX design patterns (green theme, progress bar, etc.)
- Ensure mobile responsiveness is preserved
- Add proper TypeScript typing throughout
- Use single-select radio buttons for all questions (no checkboxes)
- **API endpoint**: `/funnel/v1.0/patient/extend-tp-questionnaire`
- **Verify 42+ point threshold is achievable with proper clinical assessment**
