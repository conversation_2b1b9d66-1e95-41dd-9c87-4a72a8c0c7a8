# ExtendTP Questionnaire API Endpoints Specification

## Overview
This document specifies the backend API endpoints needed to support the ExtendTP questionnaire functionality. The frontend is already implemented and expects these endpoints to be available.

## Database Table
The `extend_tp_questionnaire` table should be created using the provided migration script (`database_migration_extend_tp_questionnaire.sql`).

## Required API Endpoints

### 1. Submit ExtendTP Questionnaire
**Endpoint:** `POST /funnel/v1.0/patient/extend-tp-questionnaire`

**Purpose:** Submit a completed ExtendTP questionnaire with scoring

**Authentication:** Required (cookie-based, following existing patterns)

**Request Body:**
```json
{
  "questionsAndAnswers": [
    {
      "questionKey": "adherence",
      "answerValue": "always-followed",
      "score": 5
    },
    {
      "questionKey": "symptomImprovement",
      "answerValue": "significant-improvement",
      "score": 5
    },
    {
      "questionKey": "symptomFrequency",
      "answerValue": "much-less-often",
      "score": 5
    },
    {
      "questionKey": "additionalRelief",
      "answerValue": "no-none",
      "score": 5
    },
    {
      "questionKey": "functionalBenefit",
      "answerValue": "significantly-improved",
      "score": 5
    },
    {
      "questionKey": "sleepQuality",
      "answerValue": "much-improved",
      "score": 5
    },
    {
      "questionKey": "tolerance",
      "answerValue": "no-increase-needed",
      "score": 5
    },
    {
      "questionKey": "sideEffectSeverity",
      "answerValue": "none-mild",
      "score": 5
    },
    {
      "questionKey": "sideEffectTolerability",
      "answerValue": "not-at-all",
      "score": 5
    },
    {
      "questionKey": "overallSatisfaction",
      "answerValue": "very-satisfied",
      "score": 5
    },
    {
      "questionKey": "goalAchievement",
      "answerValue": "completely-met",
      "score": 5
    },
    {
      "questionKey": "treatmentIntent",
      "answerValue": "continue-current",
      "score": 5
    }
  ],
  "totalScore": 60,
  "maxScore": 60,
  "isEligible": true,
  "submittedAt": "2025-01-07T10:30:00Z"
}
```

**Response:**
```json
{
  "success": true,
  "message": "ExtendTP questionnaire submitted successfully",
  "data": {
    "id": "uuid-here",
    "totalScore": 60,
    "isEligible": true,
    "status": "submitted"
  }
}
```

**Implementation Notes:**
- Extract user email from authentication session/cookie
- Look up patient_id from the `patient` table using email
- Store the form data in the `questionnaire_data` JSONB column
- Set `total_score`, `is_eligible`, and `status` fields
- Include IP address and user agent in metadata
- Return success response with questionnaire ID and eligibility status

### 2. Get ExtendTP Questionnaire Status
**Endpoint:** `GET /funnel/v1.0/patient/extend-tp-questionnaire/status?email={email}`

**Purpose:** Check if user has completed the ExtendTP questionnaire and get their status

**Authentication:** Required (cookie-based)

**Query Parameters:**
- `email` (required): Patient email address

**Response (Completed):**
```json
{
  "success": true,
  "questionnaire": {
    "id": "uuid-here",
    "totalScore": 60,
    "maxScore": 60,
    "isEligible": true,
    "status": "submitted",
    "submittedAt": "2025-01-07T10:30:00Z",
    "reviewedAt": null,
    "approvedAt": null
  }
}
```

**Response (Not Completed):**
```json
{
  "success": false,
  "message": "No ExtendTP questionnaire found for this patient"
}
```

**Implementation Notes:**
- Query the `extend_tp_questionnaire` table by email
- Return the most recent submission if multiple exist
- Include all relevant status and scoring information

## Database Operations

### Insert New Questionnaire
```sql
INSERT INTO extend_tp_questionnaire (
    patient_id, 
    email, 
    zoho_id,
    questionnaire_data, 
    total_score, 
    max_score, 
    is_eligible,
    status
) VALUES (
    $1, $2, $3, $4, $5, $6, $7, 'submitted'
);
```

### Query Questionnaire Status
```sql
SELECT 
    id,
    total_score,
    max_score,
    is_eligible,
    status,
    created_at as submitted_at,
    reviewed_at,
    approved_at
FROM extend_tp_questionnaire 
WHERE email = $1 
ORDER BY created_at DESC 
LIMIT 1;
```

## Scoring Logic
- **Total Possible Score:** 60 points (12 questions × 5 points each)
- **Eligibility Threshold:** 42+ points (70% of total)
- **Outcome:** Score ≥ 42 = Eligible for 6-month treatment plan via telehealth
- **Outcome:** Score < 42 = Requires face-to-face consultation before continuing

## Error Handling

### Common Error Responses
```json
{
  "success": false,
  "error": "Authentication required",
  "code": 401
}
```

```json
{
  "success": false,
  "error": "Invalid questionnaire data",
  "code": 400
}
```

```json
{
  "success": false,
  "error": "Patient not found",
  "code": 404
}
```

## Security Considerations
- Validate all input data before storing
- Ensure user can only access their own questionnaire data
- Log all questionnaire submissions for audit purposes
- Rate limit submissions to prevent abuse

## Integration Points
- **Patient Authentication:** Use existing cookie-based auth system
- **Patient Lookup:** Query `patient` table to get `patientID` and `zohoID`
- **Logging:** Follow existing logging patterns for API requests
- **Error Handling:** Use consistent error response format

## Testing
- Test with valid questionnaire data
- Test with invalid/missing data
- Test authentication requirements
- Test duplicate submissions (should update existing or create new?)
- Test edge cases (missing patient, invalid email, etc.)
- Test scoring calculations and eligibility determination
