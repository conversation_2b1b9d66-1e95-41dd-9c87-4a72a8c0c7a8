-- Migration: Add missing columns to thc_increase_questionnaire table
-- Run this if the table was already created without ip_address and user_agent columns

-- Add ip_address column if it doesn't exist
DO $$ 
BEGIN 
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'thc_increase_questionnaire' 
        AND column_name = 'ip_address'
    ) THEN
        ALTER TABLE thc_increase_questionnaire 
        ADD COLUMN ip_address TEXT;
    END IF;
END $$;

-- Add user_agent column if it doesn't exist
DO $$ 
BEGIN 
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'thc_increase_questionnaire' 
        AND column_name = 'user_agent'
    ) THEN
        ALTER TABLE thc_increase_questionnaire 
        ADD COLUMN user_agent TEXT;
    END IF;
END $$;

-- Add comments for the new columns
COMMENT ON COLUMN thc_increase_questionnaire.ip_address IS 'IP address of the user who submitted the questionnaire';
COMMENT ON COLUMN thc_increase_questionnaire.user_agent IS 'User agent string of the browser used to submit the questionnaire';

-- Verify the columns were added
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'thc_increase_questionnaire' 
AND column_name IN ('ip_address', 'user_agent')
ORDER BY column_name;
