-- Migration: Create quantity_increase_questionnaire table
-- Purpose: Store quantity increase questionnaire responses and status
-- Date: 2025-01-17

-- Create the quantity_increase_questionnaire table
CREATE TABLE IF NOT EXISTS quantity_increase_questionnaire (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    patient_id UUID REFERENCES patient(id) ON DELETE CASCADE,
    email VARCHAR(255) NOT NULL,
    zoho_id VARCHAR(255),
    
    -- Questionnaire data stored as JSONB for flexibility
    questionnaire_data JSONB NOT NULL,
    
    -- Quantity increase specific fields (supports multiple strengths)
    selected_strengths TEXT[] NOT NULL CHECK (array_length(selected_strengths, 1) > 0),
    strength_requests JSONB NOT NULL, -- Array of {strength, currentQuantity, requestedQuantity, increaseAmount}
    
    -- Scoring information
    total_score INTEGER NOT NULL CHECK (total_score >= 0),
    max_score INTEGER NOT NULL DEFAULT 50 CHECK (max_score > 0),
    is_eligible BOOLEAN NOT NULL DEFAULT false,
    
    -- Status tracking
    status VARCHAR(20) NOT NULL DEFAULT 'submitted' CHECK (status IN ('submitted', 'under_review', 'approved', 'rejected')),
    
    -- Review information
    reviewed_by VARCHAR(255),
    reviewed_at TIMESTAMP WITH TIME ZONE,
    review_notes TEXT,
    
    -- Metadata
    ip_address INET,
    user_agent TEXT,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_quantity_increase_questionnaire_patient_id ON quantity_increase_questionnaire(patient_id);
CREATE INDEX IF NOT EXISTS idx_quantity_increase_questionnaire_email ON quantity_increase_questionnaire(email);
CREATE INDEX IF NOT EXISTS idx_quantity_increase_questionnaire_zoho_id ON quantity_increase_questionnaire(zoho_id);
CREATE INDEX IF NOT EXISTS idx_quantity_increase_questionnaire_status ON quantity_increase_questionnaire(status);
CREATE INDEX IF NOT EXISTS idx_quantity_increase_questionnaire_created_at ON quantity_increase_questionnaire(created_at);
CREATE INDEX IF NOT EXISTS idx_quantity_increase_questionnaire_selected_strengths ON quantity_increase_questionnaire USING GIN(selected_strengths);

-- Create a composite index for common queries
CREATE INDEX IF NOT EXISTS idx_quantity_increase_questionnaire_email_status ON quantity_increase_questionnaire(email, status);

-- Create a trigger to automatically update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_quantity_increase_questionnaire_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_quantity_increase_questionnaire_updated_at
    BEFORE UPDATE ON quantity_increase_questionnaire
    FOR EACH ROW
    EXECUTE FUNCTION update_quantity_increase_questionnaire_updated_at();

-- Add constraints to ensure valid strength selections
ALTER TABLE quantity_increase_questionnaire
ADD CONSTRAINT check_valid_strengths
CHECK (
    selected_strengths <@ ARRAY['22', '29'] AND
    array_length(selected_strengths, 1) > 0
);

-- Add constraint to ensure strength_requests structure is valid
ALTER TABLE quantity_increase_questionnaire
ADD CONSTRAINT check_strength_requests_structure
CHECK (
    jsonb_typeof(strength_requests) = 'array' AND
    jsonb_array_length(strength_requests) > 0
);

-- Add constraint to ensure score is within valid range
ALTER TABLE quantity_increase_questionnaire 
ADD CONSTRAINT check_score_range 
CHECK (total_score <= max_score);

-- Create a view for easier querying of questionnaire summaries
CREATE OR REPLACE VIEW quantity_increase_questionnaire_summary AS
SELECT
    id,
    email,
    selected_strengths,
    strength_requests,
    total_score,
    max_score,
    ROUND((total_score::DECIMAL / max_score::DECIMAL) * 100, 1) as score_percentage,
    is_eligible,
    status,
    reviewed_by,
    reviewed_at,
    created_at,
    updated_at
FROM quantity_increase_questionnaire
ORDER BY created_at DESC;

-- Grant appropriate permissions (adjust based on your user roles)
-- GRANT SELECT, INSERT, UPDATE ON quantity_increase_questionnaire TO app_user;
-- GRANT SELECT ON quantity_increase_questionnaire_summary TO app_user;

-- Add comments for documentation
COMMENT ON TABLE quantity_increase_questionnaire IS 'Stores quantity increase questionnaire responses and approval status';
COMMENT ON COLUMN quantity_increase_questionnaire.questionnaire_data IS 'Complete questionnaire response data in JSONB format';
COMMENT ON COLUMN quantity_increase_questionnaire.selected_strengths IS 'Array of THC strengths for which increases are requested (22, 29, or both)';
COMMENT ON COLUMN quantity_increase_questionnaire.strength_requests IS 'JSONB array of strength request objects with currentQuantity, requestedQuantity, and increaseAmount';
COMMENT ON COLUMN quantity_increase_questionnaire.total_score IS 'Total questionnaire score achieved';
COMMENT ON COLUMN quantity_increase_questionnaire.is_eligible IS 'Whether patient meets eligibility threshold based on score';
COMMENT ON COLUMN quantity_increase_questionnaire.status IS 'Current status: submitted, under_review, approved, rejected';

-- Sample data for testing (remove in production)
/*
INSERT INTO quantity_increase_questionnaire (
    patient_id,
    email,
    questionnaire_data,
    selected_strength,
    current_quantity,
    requested_quantity,
    total_score,
    max_score,
    is_eligible,
    status
) VALUES (
    '00000000-0000-0000-0000-000000000001'::UUID,
    '<EMAIL>',
    '{"questionsAndAnswers": [{"questionKey": "reasonForRequest", "questionText": "Why are you requesting an increase?", "answerValue": ["Not lasting full month"], "answerText": "Not lasting full month", "score": 3}], "submittedAt": "2025-01-17T10:30:00Z"}'::JSONB,
    '29',
    28,
    42,
    35,
    50,
    true,
    'submitted'
);
*/
