-- Add missing "reasonOtherText" text field to quantity increase questionnaire
-- This field should appear when users select "Other reason" checkbox

-- First, let's check the current configuration
SELECT id, name, jsonb_array_length(questions) as question_count 
FROM questionnaire_configs 
WHERE id = 'quantity_increase';

-- Add the reasonOtherText question to the quantity_increase questionnaire
UPDATE questionnaire_configs 
SET questions = questions || '[{
  "key": "reasonOtherText",
  "text": "Please describe your other reason",
  "type": "text",
  "order": 6.5,
  "sectionId": "a9e927d7-1b7a-44f7-9ad7-6bb417daf291",
  "textFieldConfig": {
    "purpose": "Additional details for other reason selection",
    "required": true,
    "maxLength": 200,
    "placeholder": "Please describe your other reason..."
  },
  "contributesToScore": false
}]'::jsonb,
last_modified = NOW(),
modified_by = 'admin_fix_other_text_field'
WHERE id = 'quantity_increase';

-- Verify the update was successful
SELECT id, name, jsonb_array_length(questions) as question_count 
FROM questionnaire_configs 
WHERE id = 'quantity_increase';

-- Show the newly added question
SELECT jsonb_pretty(question) as new_question
FROM questionnaire_configs,
     jsonb_array_elements(questions) as question
WHERE id = 'quantity_increase' 
  AND question->>'key' = 'reasonOtherText';

-- Optional: Show all questions in order to verify placement
SELECT 
  question->>'key' as question_key,
  question->>'text' as question_text,
  question->>'type' as question_type,
  (question->>'order')::numeric as question_order
FROM questionnaire_configs,
     jsonb_array_elements(questions) as question
WHERE id = 'quantity_increase'
ORDER BY (question->>'order')::numeric;
