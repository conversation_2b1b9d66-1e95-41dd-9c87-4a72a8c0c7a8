{"questionnaire": "Request Quantity Increase", "description": "Questionnaire for patients requesting to increase the quantity of their current THC treatment (22% or 29%)", "totalSteps": 5, "maxScore": 50, "passingScore": 35, "questions": [{"step": 1, "questionKey": "strengthSelectionAndReasons", "questionText": "Strength Selection and Reasons for Increase", "questionType": "dynamic_checkbox_and_checkbox", "parts": [{"partKey": "strengthSelection", "partText": "Which strength(s) would you like to increase? (Select all that apply)", "type": "dynamic_checkbox_with_quantity", "note": "Options are dynamically generated based on patient's current treatment plan", "dynamicOptions": {"22% THC": {"condition": "Patient has active 22% THC treatment below maximum quantity", "availableQuantities": "Based on current quantity: 14g→28g→42g→56g→70g→84g"}, "29% THC": {"condition": "Patient has active 29% THC treatment below maximum quantity", "availableQuantities": "Based on current quantity: 14g→28g→42g→56g→70g→84g"}}}, {"partKey": "reasonForRequest", "partText": "Why are you requesting an increase in the amount of medicinal cannabis approved under your current treatment plan? (Select all that apply)", "type": "checkbox_multiple", "options": [{"key": "reasonNotLasting", "text": "The current quantity is not lasting the full month", "score": 3}, {"key": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "text": "I need more frequent or higher doses to manage symptoms", "score": 2}, {"key": "reasonTolerance", "text": "I have developed tolerance and need more to achieve the same effect", "score": 2}, {"key": "reasonIncreasedSymptoms", "text": "My symptoms have worsened or become more frequent", "score": 3}, {"key": "reasonOther", "text": "Other (please describe):", "score": 1, "hasTextField": true, "textFieldKey": "reasonOtherText", "textFieldPlaceholder": "Please describe your other reason..."}], "maxScore": 8, "scoringNote": "Maximum 8 points total for all selected reasons"}]}, {"step": 2, "questionKey": "currentTreatmentResponse", "questionText": "Response to Current Treatment", "questionType": "slider_checkbox_radio", "parts": [{"partKey": "currentEffectiveness", "partText": "On a scale of 1 to 10, how well has your current quantity helped manage your symptoms?", "type": "slider", "subtitle": "Rate your symptom improvement (1 = no benefit, 10 = complete relief, but still requiring more quantity):", "min": 1, "max": 10, "step": 1, "scoring": "Scaled from 1-10 to 0-6 points: Math.floor((value - 1) * 6 / 9)", "maxScore": 6}, {"partKey": "sideEffects", "partText": "Have you experienced any side effects with your current dose? (Select all that apply)", "type": "checkbox_multiple", "options": [{"key": "sideEffectsNone", "text": "None", "score": 4}, {"key": "sideEffectsMild", "text": "Mild (e.g. dry mouth, light drowsiness)", "score": 3}, {"key": "sideEffectsModerate", "text": "Moderate (e.g. dizziness, nausea, appetite changes)", "score": 2, "requiresDescription": true}, {"key": "sideEffectsStrong", "text": "Strong (e.g. anxiety, confusion, racing heart)", "score": 0, "requiresDescription": true}], "conditionalTextField": {"key": "sideEffectsDescription", "placeholder": "Please describe your side effects...", "showWhen": ["sideEffectsModerate", "sideEffectsStrong"], "required": true}, "scoringNote": "Penalty system - only highest penalty applies", "maxScore": 4}, {"partKey": "usageConsistency", "partText": "Have you been using your current prescribed amount consistently?", "type": "radio_with_text", "options": [{"value": "full-amount", "text": "Yes – I use my full monthly amount regularly", "score": 6}, {"value": "varies", "text": "It varies month to month", "score": 4}, {"value": "leftover", "text": "No – I sometimes have leftover product", "score": 1}, {"value": "other", "text": "Other (please explain)", "score": 2, "hasTextField": true, "textFieldKey": "usageConsistencyOther", "textFieldPlaceholder": "Please explain..."}], "maxScore": 6}]}, {"step": 3, "questionKey": "healthChanges", "questionText": "Have there been any changes in your health, medications, or lifestyle since your last doctor review?", "questionType": "radio_with_conditional", "options": [{"value": "no-changes", "text": "No changes", "score": 3}, {"value": "yes", "text": "Yes – please describe", "score": 1, "hasTextField": true, "textFieldKey": "healthChangesDescription", "textFieldPlaceholder": "Please describe the changes...", "textFieldRows": 4}], "maxScore": 3}, {"step": 4, "questionKey": "expectationsAndPreferences", "questionText": "Expectations and Usage Preferences", "questionType": "text_and_radio", "parts": [{"partKey": "expectations", "partText": "What do you hope to achieve by increasing the quantity of your medicinal cannabis?", "type": "textarea", "placeholder": "Describe your expectations and goals...", "rows": 3, "required": true, "scoring": "Length-based: >10 chars = 2 points, >0 chars = 1 point, empty = 0 points", "maxScore": 2}, {"partKey": "concerns", "partText": "Do you have any concerns about increasing the quantity?", "type": "textarea", "placeholder": "e.g. potential for dependency, reduced effect over time, cost...", "rows": 3, "required": true, "scoring": "Length-based: >10 chars = 2 points, >0 chars = 1 point, empty = 0 points", "maxScore": 2}, {"partKey": "intendedUsage", "partText": "How do you intend to use the increased quantity?", "type": "radio_with_text", "required": true, "options": [{"value": "worsening-symptoms", "text": "To manage worsening or more frequent symptoms", "score": 4}, {"value": "breakthrough-symptoms", "text": "For occasional flare-ups or breakthrough symptoms", "score": 4}, {"value": "extend-daily", "text": "To extend daily use across more time periods", "score": 3}, {"value": "gradual-increase", "text": "To gradually increase my dose (within doctor's guidance)", "score": 3}, {"value": "unsure-advice", "text": "Unsure – I would like advice from my doctor", "score": 2}, {"value": "other", "text": "Other (please describe)", "score": 1, "hasTextField": true, "textFieldKey": "intendedUsageOther", "textFieldPlaceholder": "Please describe..."}], "maxScore": 4}]}, {"step": 5, "questionKey": "consent", "questionText": "Do you consent to your doctor reviewing this information and accessing your My Health Record (if needed) to determine whether a quantity increase is clinically appropriate?", "questionType": "radio", "options": [{"value": "yes", "text": "Yes", "score": 5}, {"value": "no", "text": "No", "score": 0}], "maxScore": 5}], "formDataStructure": {"reasonNotLasting": "boolean", "reasonHigherDoses": "boolean", "reasonTolerance": "boolean", "reasonIncreasedSymptoms": "boolean", "reasonOther": "boolean", "reasonOtherText": "string", "currentEffectiveness": "string (1-10)", "sideEffectsNone": "boolean", "sideEffectsMild": "boolean", "sideEffectsModerate": "boolean", "sideEffectsStrong": "boolean", "sideEffectsDescription": "string", "usageConsistency": "string (full-amount|leftover|varies|other)", "usageConsistencyOther": "string", "healthChanges": "string (no-changes|yes)", "healthChangesDescription": "string", "expectations": "string", "concerns": "string", "intendedUsage": "string (extend-daily|worsening-symptoms|breakthrough-symptoms|gradual-increase|unsure-advice|other)", "intendedUsageOther": "string", "consent": "string (yes|no)", "selectedStrengths": "string[] (['22', '29'])", "thc22Selected": "boolean", "thc22CurrentQuantity": "number", "thc22RequestedQuantity": "number", "thc29Selected": "boolean", "thc29CurrentQuantity": "number", "thc29RequestedQuantity": "number"}, "validationRules": {"step1": "At least one strength must be selected AND at least one reason must be selected", "step2": "Current effectiveness rating, at least one side effect option, and usage consistency must be answered", "step3": "Health changes selection required", "step4": "All three fields must be filled (expectations, concerns, intended usage)", "step5": "Consent must be given"}, "dynamicBehavior": {"strengthOptions": "Generated based on patient's current treatment plan and available quantity levels", "quantityLevels": [14, 28, 42, 56, 70, 84], "eligibilityCheck": "Patient must have active treatment plan with quantities below maximum", "multipleSelections": "Patients can increase both 22% and 29% THC simultaneously"}, "scoringNotes": {"totalMaxScore": 50, "passingThreshold": 35, "passingPercentage": "70% of maximum score", "eligibilityMessage": "Score >= 35 points qualifies for quantity increase approval", "nonScoringFields": ["reasonOtherText", "sideEffectsDescription", "usageConsistencyOther", "healthChangesDescription", "intendedUsageOther"], "scoringBreakdown": {"reasonForRequest": "Max 8 points (multiple reasons can be selected)", "currentEffectiveness": "Max 6 points (1-10 scale converted to 0-6)", "sideEffects": "Max 4 points (penalty system - worst side effect determines score)", "usageConsistency": "Max 6 points (full usage = highest score)", "healthChanges": "Max 3 points (no changes = higher score)", "expectations": "Max 2 points (based on response length)", "concerns": "Max 2 points (based on response length)", "intendedUsage": "Max 4 points (symptom management = highest scores)", "consent": "Max 5 points (required for approval)"}}}