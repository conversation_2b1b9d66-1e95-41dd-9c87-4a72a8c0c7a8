-- Migration: Create Add 22% THC Questionnaire Table
-- This table stores 22% THC addition questionnaire submissions with JSONB for flexible data storage

CREATE TABLE IF NOT EXISTS add_22_thc_questionnaire (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),

    -- Patient identification (following existing patterns)
    patient_id VARCHAR REFERENCES patient("patientID"),
    email TEXT NOT NULL,
    zoho_id TEXT,

    -- Questionnaire data stored as JSON<PERSON> for flexibility
    questionnaire_data JSONB NOT NULL,

    -- Scoring information
    total_score INTEGER NOT NULL DEFAULT 0,
    max_score INTEGER NOT NULL DEFAULT 50,
    is_eligible BOOLEAN NOT NULL DEFAULT FALSE,

    -- Status tracking
    status TEXT NOT NULL DEFAULT 'submitted', -- submitted, under_review, approved, rejected
    reviewed_by TEXT, -- doctor ID who reviewed
    reviewed_at TIMESTAMP WITH TIME ZONE,
    review_notes TEXT,

    -- Approval tracking
    approved_by TEXT, -- doctor ID who approved
    approved_at TIMESTAMP WITH TIME ZONE,
    approval_notes TEXT,

    -- Submission metadata
    ip_address TEXT,
    user_agent TEXT,

    -- Timestamps (following existing patterns)
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_add_22_thc_questionnaire_email ON add_22_thc_questionnaire(email);
CREATE INDEX IF NOT EXISTS idx_add_22_thc_questionnaire_patient_id ON add_22_thc_questionnaire(patient_id);
CREATE INDEX IF NOT EXISTS idx_add_22_thc_questionnaire_status ON add_22_thc_questionnaire(status);
CREATE INDEX IF NOT EXISTS idx_add_22_thc_questionnaire_created_at ON add_22_thc_questionnaire(created_at);

-- Create GIN index for JSONB queries
CREATE INDEX IF NOT EXISTS idx_add_22_thc_questionnaire_data_gin ON add_22_thc_questionnaire USING GIN (questionnaire_data);

-- Add trigger to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION update_add_22_thc_questionnaire_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_add_22_thc_questionnaire_updated_at
    BEFORE UPDATE ON add_22_thc_questionnaire
    FOR EACH ROW
    EXECUTE FUNCTION update_add_22_thc_questionnaire_updated_at();

-- Add comments for documentation
COMMENT ON TABLE add_22_thc_questionnaire IS 'Stores 22% THC addition questionnaire submissions with scoring and approval workflow';
COMMENT ON COLUMN add_22_thc_questionnaire.questionnaire_data IS 'JSONB containing all questionnaire answers and metadata';
COMMENT ON COLUMN add_22_thc_questionnaire.total_score IS 'Calculated total score from questionnaire answers (0-50)';
COMMENT ON COLUMN add_22_thc_questionnaire.is_eligible IS 'Whether patient is eligible for 22% THC addition (score >= 35)';
COMMENT ON COLUMN add_22_thc_questionnaire.status IS 'Current status: submitted, under_review, approved, rejected';
COMMENT ON COLUMN add_22_thc_questionnaire.ip_address IS 'IP address of the user who submitted the questionnaire';
COMMENT ON COLUMN add_22_thc_questionnaire.user_agent IS 'User agent string of the browser used for submission';

-- Example of the JSONB structure that will be stored:
/*
questionnaire_data JSONB structure:
{
  "questionsAndAnswers": [
    {
      "questionKey": "reasonSideEffects",
      "questionText": "Experiencing side effects with 29% (e.g., anxiety, dizziness, increased heart rate)",
      "answerValue": true,
      "answerText": "Selected",
      "score": 3
    },
    {
      "questionKey": "symptomImprovement",
      "questionText": "How well has your current 29% THC treatment worked for you?",
      "answerValue": "8",
      "answerText": "8 - Very effective",
      "score": 4
    },
    {
      "questionKey": "healthChanges",
      "questionText": "Have there been any changes in your health, medications, or lifestyle since your last consultation?",
      "answerValue": "no-changes",
      "answerText": "No changes",
      "score": 2
    },
    {
      "questionKey": "usagePlan",
      "questionText": "How do you plan to use the 22% THC product alongside your 29%?",
      "answerValue": "alternative-situations",
      "answerText": "As an alternative for specific times/situations",
      "score": 3
    },
    {
      "questionKey": "consent",
      "questionText": "Do you consent to your doctor reviewing this information?",
      "answerValue": "yes",
      "answerText": "Yes",
      "score": 5
    }
  ],
  "scoring": {
    "questionScores": {
      "reasonSideEffects": 3,
      "symptomImprovement": 4,
      "healthChanges": 2,
      "usagePlan": 3,
      "consent": 5
    }
  },
  "metadata": {
    "submittedAt": "2025-01-17T10:30:00Z",
    "ipAddress": "***********",
    "userAgent": "Mozilla/5.0...",
    "completionTimeMinutes": 5
  }
}
*/
