-- Robust Questionnaire Database Queries
-- Handles UUID validation, JSON data, and proper error handling

-- =====================================================
-- 1. CREATE TABLES WITH PROPER CONSTRAINTS
-- =====================================================

-- Questionnaire configurations table
CREATE TABLE IF NOT EXISTS questionnaire_configs (
    id VARCHAR(50) PRIMARY KEY, -- Using VARCHAR for questionnaire type IDs like 'add_22_thc'
    name VARCHAR(255) NOT NULL,
    max_score INTEGER NOT NULL DEFAULT 0,
    threshold INTEGER NOT NULL DEFAULT 0,
    version DECIMAL(10,2) NOT NULL DEFAULT 1.0,
    sections_count INTEGER NOT NULL DEFAULT 0,
    questions JSONB NOT NULL DEFAULT '[]'::jsonb,
    sections JSONB NOT NULL DEFAULT '[]'::jsonb,
    is_active BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    last_modified TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Questionnaire sections table
CREATE TABLE IF NOT EXISTS questionnaire_sections (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    questionnaire_id VARCHAR(50) NOT NULL REFERENCES questionnaire_configs(id),
    title VARCHAR(255) NOT NULL,
    description TEXT,
    order_index INTEGER NOT NULL,
    validation_rules JSONB DEFAULT '{}'::jsonb,
    is_active BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Questionnaire questions table  
CREATE TABLE IF NOT EXISTS questionnaire_questions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    questionnaire_id VARCHAR(50) NOT NULL REFERENCES questionnaire_configs(id),
    section_id UUID REFERENCES questionnaire_sections(id),
    key VARCHAR(100) NOT NULL,
    text TEXT NOT NULL,
    type VARCHAR(50) NOT NULL CHECK (type IN ('checkbox', 'radio', 'slider', 'text')),
    order_index INTEGER NOT NULL,
    answer_options JSONB DEFAULT '[]'::jsonb,
    slider_config JSONB DEFAULT NULL,
    text_field_config JSONB DEFAULT NULL,
    depends_on_question VARCHAR(200) DEFAULT NULL,
    contributes_to_score BOOLEAN NOT NULL DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(questionnaire_id, key)
);

-- Patient questionnaire responses table
CREATE TABLE IF NOT EXISTS questionnaire_responses (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    patient_id VARCHAR(100) NOT NULL,
    questionnaire_id VARCHAR(50) NOT NULL REFERENCES questionnaire_configs(id),
    responses JSONB NOT NULL DEFAULT '{}'::jsonb,
    total_score INTEGER NOT NULL DEFAULT 0,
    max_score INTEGER NOT NULL DEFAULT 0,
    is_eligible BOOLEAN NOT NULL DEFAULT false,
    status VARCHAR(50) NOT NULL DEFAULT 'submitted' CHECK (status IN ('submitted', 'under_review', 'approved', 'rejected')),
    submitted_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    reviewed_at TIMESTAMP WITH TIME ZONE DEFAULT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- 2. HELPER FUNCTIONS
-- =====================================================

-- Function to validate and fix UUID format
CREATE OR REPLACE FUNCTION validate_uuid(input_text TEXT)
RETURNS UUID AS $$
BEGIN
    -- Try to cast to UUID, return gen_random_uuid() if invalid
    BEGIN
        RETURN input_text::UUID;
    EXCEPTION WHEN invalid_text_representation THEN
        -- Generate a new UUID if the input is invalid
        RETURN gen_random_uuid();
    END;
END;
$$ LANGUAGE plpgsql;

-- Function to safely insert questionnaire configuration
CREATE OR REPLACE FUNCTION upsert_questionnaire_config(
    p_id VARCHAR(50),
    p_name VARCHAR(255),
    p_max_score INTEGER,
    p_threshold INTEGER,
    p_version DECIMAL(10,2),
    p_sections_count INTEGER,
    p_questions JSONB,
    p_sections JSONB
)
RETURNS TABLE(success BOOLEAN, message TEXT, questionnaire_id VARCHAR(50)) AS $$
DECLARE
    v_questionnaire_id VARCHAR(50);
BEGIN
    -- Insert or update questionnaire config
    INSERT INTO questionnaire_configs (
        id, name, max_score, threshold, version, sections_count, 
        questions, sections, last_modified
    ) VALUES (
        p_id, p_name, p_max_score, p_threshold, p_version, p_sections_count,
        p_questions, p_sections, CURRENT_TIMESTAMP
    )
    ON CONFLICT (id) DO UPDATE SET
        name = EXCLUDED.name,
        max_score = EXCLUDED.max_score,
        threshold = EXCLUDED.threshold,
        version = EXCLUDED.version,
        sections_count = EXCLUDED.sections_count,
        questions = EXCLUDED.questions,
        sections = EXCLUDED.sections,
        last_modified = CURRENT_TIMESTAMP,
        updated_at = CURRENT_TIMESTAMP
    RETURNING id INTO v_questionnaire_id;

    RETURN QUERY SELECT true, 'Questionnaire configuration saved successfully', v_questionnaire_id;

EXCEPTION WHEN OTHERS THEN
    RETURN QUERY SELECT false, 'Error saving questionnaire: ' || SQLERRM, NULL::VARCHAR(50);
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 3. ROBUST INSERT QUERIES
-- =====================================================

-- Insert questionnaire sections with UUID validation
CREATE OR REPLACE FUNCTION insert_questionnaire_sections(
    p_questionnaire_id VARCHAR(50),
    p_sections JSONB
)
RETURNS TABLE(success BOOLEAN, message TEXT, inserted_count INTEGER) AS $$
DECLARE
    section_record JSONB;
    v_section_id UUID;
    v_count INTEGER := 0;
BEGIN
    -- Clear existing sections for this questionnaire
    DELETE FROM questionnaire_sections WHERE questionnaire_id = p_questionnaire_id;
    
    -- Insert each section
    FOR section_record IN SELECT * FROM jsonb_array_elements(p_sections)
    LOOP
        -- Validate and use provided UUID or generate new one
        v_section_id := validate_uuid(section_record->>'id');
        
        INSERT INTO questionnaire_sections (
            id, questionnaire_id, title, description, order_index, 
            validation_rules, is_active
        ) VALUES (
            v_section_id,
            p_questionnaire_id,
            section_record->>'title',
            section_record->>'description',
            (section_record->>'order_index')::INTEGER,
            COALESCE(section_record->'validation_rules', '{}'::jsonb),
            COALESCE((section_record->>'is_active')::BOOLEAN, true)
        );
        
        v_count := v_count + 1;
    END LOOP;
    
    RETURN QUERY SELECT true, 'Sections inserted successfully', v_count;

EXCEPTION WHEN OTHERS THEN
    RETURN QUERY SELECT false, 'Error inserting sections: ' || SQLERRM, 0;
END;
$$ LANGUAGE plpgsql;

-- Insert questionnaire questions with proper validation
CREATE OR REPLACE FUNCTION insert_questionnaire_questions(
    p_questionnaire_id VARCHAR(50),
    p_questions JSONB
)
RETURNS TABLE(success BOOLEAN, message TEXT, inserted_count INTEGER) AS $$
DECLARE
    question_record JSONB;
    v_section_id UUID;
    v_count INTEGER := 0;
BEGIN
    -- Clear existing questions for this questionnaire
    DELETE FROM questionnaire_questions WHERE questionnaire_id = p_questionnaire_id;
    
    -- Insert each question
    FOR question_record IN SELECT * FROM jsonb_array_elements(p_questions)
    LOOP
        -- Validate section ID
        v_section_id := validate_uuid(question_record->>'sectionId');
        
        INSERT INTO questionnaire_questions (
            questionnaire_id, section_id, key, text, type, order_index,
            answer_options, slider_config, text_field_config, 
            depends_on_question, contributes_to_score
        ) VALUES (
            p_questionnaire_id,
            v_section_id,
            question_record->>'key',
            question_record->>'text',
            question_record->>'type',
            (question_record->>'order')::INTEGER,
            COALESCE(question_record->'answerOptions', '[]'::jsonb),
            question_record->'sliderConfig',
            question_record->'textFieldConfig',
            question_record->>'dependsOnQuestion',
            COALESCE((question_record->>'contributesToScore')::BOOLEAN, false)
        );
        
        v_count := v_count + 1;
    END LOOP;
    
    RETURN QUERY SELECT true, 'Questions inserted successfully', v_count;

EXCEPTION WHEN OTHERS THEN
    RETURN QUERY SELECT false, 'Error inserting questions: ' || SQLERRM, 0;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 4. COMPLETE QUESTIONNAIRE SETUP
-- =====================================================

-- Complete function to set up questionnaire from JSON data
CREATE OR REPLACE FUNCTION setup_questionnaire_from_json(questionnaire_data JSONB)
RETURNS TABLE(success BOOLEAN, message TEXT, details JSONB) AS $$
DECLARE
    v_config_result RECORD;
    v_sections_result RECORD;
    v_questions_result RECORD;
    v_questionnaire_id VARCHAR(50);
    v_details JSONB := '{}'::jsonb;
BEGIN
    -- Extract questionnaire ID
    v_questionnaire_id := questionnaire_data->'data'->>'id';
    
    -- 1. Insert/Update questionnaire config
    SELECT * INTO v_config_result FROM upsert_questionnaire_config(
        v_questionnaire_id,
        questionnaire_data->'data'->>'name',
        (questionnaire_data->'data'->>'maxScore')::INTEGER,
        (questionnaire_data->'data'->>'threshold')::INTEGER,
        (questionnaire_data->'data'->>'version')::DECIMAL,
        (questionnaire_data->'data'->>'sectionsCount')::INTEGER,
        questionnaire_data->'data'->'questions',
        questionnaire_data->'data'->'sections'
    );
    
    IF NOT v_config_result.success THEN
        RETURN QUERY SELECT false, v_config_result.message, v_details;
        RETURN;
    END IF;
    
    -- 2. Insert sections
    SELECT * INTO v_sections_result FROM insert_questionnaire_sections(
        v_questionnaire_id,
        questionnaire_data->'data'->'sections'
    );
    
    -- 3. Insert questions
    SELECT * INTO v_questions_result FROM insert_questionnaire_questions(
        v_questionnaire_id,
        questionnaire_data->'data'->'questions'
    );
    
    -- Build details
    v_details := jsonb_build_object(
        'questionnaire_id', v_questionnaire_id,
        'config_success', v_config_result.success,
        'sections_inserted', v_sections_result.inserted_count,
        'questions_inserted', v_questions_result.inserted_count,
        'sections_success', v_sections_result.success,
        'questions_success', v_questions_result.success
    );
    
    -- Return overall result
    IF v_config_result.success AND v_sections_result.success AND v_questions_result.success THEN
        RETURN QUERY SELECT true, 'Questionnaire setup completed successfully', v_details;
    ELSE
        RETURN QUERY SELECT false, 'Questionnaire setup completed with errors', v_details;
    END IF;

EXCEPTION WHEN OTHERS THEN
    RETURN QUERY SELECT false, 'Error setting up questionnaire: ' || SQLERRM, v_details;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 5. EXAMPLE USAGE
-- =====================================================

/*
-- Example: Setup questionnaire from your JSON data
SELECT * FROM setup_questionnaire_from_json('
{
  "success": true,
  "message": "Questionnaire configuration retrieved successfully",
  "data": {
    "id": "add_22_thc",
    "name": "Add 22% THC Option",
    "maxScore": 33,
    "threshold": 15,
    "version": "1.6",
    "sectionsCount": 5,
    "questions": [...],
    "sections": [...]
  }
}'::jsonb);

-- Query questionnaire config
SELECT * FROM questionnaire_configs WHERE id = 'add_22_thc';

-- Query sections for a questionnaire
SELECT * FROM questionnaire_sections 
WHERE questionnaire_id = 'add_22_thc' 
ORDER BY order_index;

-- Query questions for a questionnaire
SELECT * FROM questionnaire_questions 
WHERE questionnaire_id = 'add_22_thc' 
ORDER BY order_index;
*/
