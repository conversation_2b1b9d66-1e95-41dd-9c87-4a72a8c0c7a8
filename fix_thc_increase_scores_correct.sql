-- Fix THC Increase Questionnaire Scores - CORRECTED VERSION
-- This script works with the actual data structure where questionnaire_data is a JSON array

-- =====================================================
-- STEP 1: Create function to recalculate individual scores
-- =====================================================

CREATE OR REPLACE FUNCTION calculate_thc_score(question_key TEXT, answer_value TEXT)
RETURNS INTEGER AS $$
BEGIN
    CASE question_key
        WHEN 'consistency' THEN
            CASE answer_value
                WHEN 'every-day' THEN RETURN 3;
                WHEN 'most-days' THEN RETURN 2;
                WHEN 'few-times-week' THEN RETURN 1;
                WHEN 'once-week' THEN RETURN 0;
                WHEN 'rarely' THEN RETURN 0;  -- Added this based on your data
                ELSE RETURN 0;
            END CASE;
        
        WHEN 'dosage' THEN
            CASE answer_value
                WHEN 'less-than-0-5g' THEN RETURN 1;
                WHEN '0-5g-1g' THEN RETURN 2;  -- Fixed: should be 2 points
                WHEN '1g-2g' THEN RETURN 3;
                WHEN 'more-than-2g' THEN RETURN 4;
                ELSE RETURN 0;
            END CASE;

        WHEN 'frequency' THEN
            CASE answer_value
                WHEN 'once-a-day' THEN RETURN 1;
                WHEN 'twice-a-day' THEN RETURN 2;
                WHEN 'three-times-a-day' THEN RETURN 3;
                WHEN 'as-needed' THEN RETURN 1;  -- Fixed: should be 1 point
                ELSE RETURN 0;
            END CASE;
        
        WHEN 'condition' THEN
            -- 2 points if not 'other' and not null/empty
            IF answer_value != 'other' AND answer_value IS NOT NULL AND answer_value != '' THEN
                RETURN 2;
            ELSE
                RETURN 0;
            END IF;
        
        WHEN 'effectiveness' THEN
            CASE answer_value
                WHEN '1-2' THEN RETURN 0;
                WHEN '3-4' THEN RETURN 1;
                WHEN '5-6' THEN RETURN 2;
                WHEN '7-8' THEN RETURN 3;
                WHEN '9-10' THEN RETURN 4;
                ELSE RETURN 0;
            END CASE;
        
        WHEN 'symptomChanges' THEN
            CASE answer_value
                WHEN 'significant-improvement' THEN RETURN 4;
                WHEN 'some-improvement' THEN RETURN 3;
                WHEN 'no-change' THEN RETURN 2;
                WHEN 'worsening-symptoms' THEN RETURN 0;
                ELSE RETURN 0;
            END CASE;
        
        WHEN 'sideEffect' THEN
            -- 4 points for 'none', 1 point for other specific side effects
            IF answer_value = 'none' THEN
                RETURN 4;
            ELSIF answer_value != 'other' AND answer_value IS NOT NULL AND answer_value != '' THEN
                RETURN 1;
            ELSE
                RETURN 0;
            END IF;
        
        WHEN 'sideEffectManageability' THEN
            CASE answer_value
                WHEN '1-2' THEN RETURN 0;
                WHEN '3-4' THEN RETURN 1;
                WHEN '5-6' THEN RETURN 2;
                WHEN '7-8' THEN RETURN 3;
                WHEN '9-10' THEN RETURN 4;
                ELSE RETURN 0;
            END CASE;
        
        WHEN 'concerns' THEN
            CASE answer_value
                WHEN 'no' THEN RETURN 3;
                WHEN 'yes' THEN RETURN 0;
                ELSE RETURN 0;
            END CASE;
        
        WHEN 'treatmentEffectiveness' THEN
            CASE answer_value
                WHEN 'very-effective' THEN RETURN 4;
                WHEN 'somewhat-effective' THEN RETURN 3;
                WHEN 'not-effective' THEN RETURN 0;
                ELSE RETURN 0;
            END CASE;
        
        WHEN 'weaknessAssessment' THEN
            CASE answer_value
                WHEN 'yes-definitely' THEN RETURN 4;
                WHEN 'yes-somewhat' THEN RETURN 3;
                WHEN 'no-adequate' THEN RETURN 1;
                WHEN 'no-too-strong' THEN RETURN 0;  -- Fixed: should be 0 points
                ELSE RETURN 0;
            END CASE;

        WHEN 'insufficientRelief' THEN
            CASE answer_value
                WHEN 'yes-definitely' THEN RETURN 4;
                WHEN 'yes-somewhat' THEN RETURN 3;
                WHEN 'no-adequate' THEN RETURN 1;
                WHEN 'no-complete-relief' THEN RETURN 0;  -- Fixed: should be 0 points
                ELSE RETURN 0;
            END CASE;
        
        WHEN 'satisfactionWithForm' THEN
            CASE answer_value
                WHEN 'very-satisfied' THEN RETURN 4;
                WHEN 'somewhat-satisfied' THEN RETURN 3;
                WHEN 'neutral' THEN RETURN 2;
                WHEN 'somewhat-unsatisfied' THEN RETURN 1;
                WHEN 'very-unsatisfied' THEN RETURN 0;
                ELSE RETURN 0;
            END CASE;
        
        WHEN 'openToHigherPotency' THEN
            CASE answer_value
                WHEN 'yes' THEN RETURN 3;
                WHEN 'maybe' THEN RETURN 2;
                WHEN 'no' THEN RETURN 0;
                ELSE RETURN 0;
            END CASE;
        
        WHEN 'quickReliefImportance' THEN
            CASE answer_value
                WHEN 'very-important' THEN RETURN 4;
                WHEN 'important' THEN RETURN 3;
                WHEN 'neutral' THEN RETURN 2;
                WHEN 'not-important' THEN RETURN 1;  -- Fixed: should be 1 point
                ELSE RETURN 0;
            END CASE;
        
        WHEN 'continueTreatment' THEN
            CASE answer_value
                WHEN 'very-likely' THEN RETURN 3;
                WHEN 'somewhat-likely' THEN RETURN 2;
                WHEN 'neutral' THEN RETURN 1;
                WHEN 'unlikely' THEN RETURN 0;
                WHEN 'very-unlikely' THEN RETURN 0;  -- Added based on your data
                ELSE RETURN 0;
            END CASE;
        
        WHEN 'overallSatisfaction' THEN
            CASE answer_value
                WHEN 'very-satisfied' THEN RETURN 4;
                WHEN 'somewhat-satisfied' THEN RETURN 3;
                WHEN 'neutral' THEN RETURN 2;
                WHEN 'somewhat-unsatisfied' THEN RETURN 1;
                WHEN 'very-unsatisfied' THEN RETURN 0;
                ELSE RETURN 0;
            END CASE;
        
        ELSE
            RETURN 0;
    END CASE;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- STEP 2: Test the scoring function on existing data
-- =====================================================

-- Test scoring calculation on one record to verify it works
SELECT 
    id,
    email,
    total_score as current_total_score,
    (
        SELECT SUM(calculate_thc_score(
            question->>'questionKey', 
            question->>'answerValue'
        ))
        FROM jsonb_array_elements(questionnaire_data) AS question
    ) as calculated_total_score
FROM thc_increase_questionnaire 
ORDER BY created_at DESC 
LIMIT 3;

-- =====================================================
-- STEP 3: Update individual question scores
-- =====================================================

-- Update each question's score in the questionnaire_data array
UPDATE thc_increase_questionnaire 
SET questionnaire_data = (
    SELECT jsonb_agg(
        jsonb_set(
            question,
            '{score}',
            to_jsonb(calculate_thc_score(question->>'questionKey', question->>'answerValue'))
        )
    )
    FROM jsonb_array_elements(questionnaire_data) AS question
),
updated_at = NOW();

-- =====================================================
-- STEP 4: Recalculate total scores and eligibility
-- =====================================================

-- Update total_score and is_eligible based on the new individual scores
UPDATE thc_increase_questionnaire 
SET 
    total_score = (
        SELECT COALESCE(SUM((question->>'score')::INTEGER), 0)
        FROM jsonb_array_elements(questionnaire_data) AS question
    ),
    is_eligible = (
        SELECT COALESCE(SUM((question->>'score')::INTEGER), 0) >= 45  -- Keep original 45 threshold
        FROM jsonb_array_elements(questionnaire_data) AS question
    ),
    updated_at = NOW();

-- =====================================================
-- STEP 5: Verify the results
-- =====================================================

-- Check the results
SELECT 
    COUNT(*) as total_records,
    COUNT(CASE WHEN total_score > 0 THEN 1 END) as records_with_scores,
    COUNT(CASE WHEN is_eligible = true THEN 1 END) as eligible_records,
    AVG(total_score) as avg_score,
    MIN(total_score) as min_score,
    MAX(total_score) as max_score
FROM thc_increase_questionnaire;

-- Show sample of updated records
SELECT 
    id,
    email,
    total_score,
    is_eligible,
    (questionnaire_data->0->>'score')::INTEGER as first_question_score,
    questionnaire_data->0->>'questionKey' as first_question_key,
    questionnaire_data->0->>'answerValue' as first_answer_value,
    updated_at
FROM thc_increase_questionnaire 
ORDER BY updated_at DESC 
LIMIT 5;

-- Show detailed breakdown for one record
SELECT 
    id,
    email,
    total_score,
    question->>'questionKey' as question_key,
    question->>'answerValue' as answer_value,
    (question->>'score')::INTEGER as score
FROM thc_increase_questionnaire,
     jsonb_array_elements(questionnaire_data) AS question
WHERE id = (SELECT id FROM thc_increase_questionnaire ORDER BY created_at DESC LIMIT 1)
ORDER BY (question->>'questionKey');
