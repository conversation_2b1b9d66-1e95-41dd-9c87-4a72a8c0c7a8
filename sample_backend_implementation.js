// Sample Backend Implementation for THC Increase Questionnaire API Endpoints
// This is a Node.js/Express implementation example

const express = require('express');
const { Pool } = require('pg');
const router = express.Router();

// Database connection (adjust connection details as needed)
const pool = new Pool({
  // Your database connection configuration
});

// Middleware to extract user from session/cookie (adjust based on your auth system)
const authenticateUser = async (req, res, next) => {
  try {
    // Extract user from session/cookie - adjust based on your auth implementation
    const userEmail = req.session?.user?.email || req.user?.email;
    
    if (!userEmail) {
      return res.status(401).json({
        success: false,
        error: "Authentication required",
        code: 401
      });
    }
    
    req.userEmail = userEmail;
    next();
  } catch (error) {
    console.error('Authentication error:', error);
    res.status(401).json({
      success: false,
      error: "Authentication failed",
      code: 401
    });
  }
};

// POST /funnel/v1.0/patient/thc-increase-questionnaire
router.post('/thc-increase-questionnaire', authenticateUser, async (req, res) => {
  const client = await pool.connect();
  
  try {
    await client.query('BEGIN');
    
    const {
      questionsAndAnswers,
      totalScore,
      maxScore,
      isEligible,
      submittedAt
    } = req.body;
    
    // Validate required fields
    if (!totalScore || totalScore < 0 || totalScore > 61) {
      return res.status(400).json({
        success: false,
        error: "Invalid total score",
        code: 400
      });
    }

    if (!questionsAndAnswers || !Array.isArray(questionsAndAnswers)) {
      return res.status(400).json({
        success: false,
        error: "Questions and answers are required",
        code: 400
      });
    }

    // Get patient information
    const patientQuery = `
      SELECT "patientID", "zohoID"
      FROM patient
      WHERE email = $1
    `;
    const patientResult = await client.query(patientQuery, [req.userEmail]);

    if (patientResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        error: "Patient not found",
        code: 404
      });
    }

    const { patientID, zohoID } = patientResult.rows[0];

    // Extract IP address and user agent for separate columns
    const ipAddress = req.ip || req.connection.remoteAddress || req.headers['x-forwarded-for'] || 'unknown';
    const userAgent = req.get('User-Agent') || 'unknown';

    // Prepare questionnaire data for JSONB storage
    const questionnaireData = {
      questionsAndAnswers: questionsAndAnswers,
      scoring: {
        questionScores: questionsAndAnswers.reduce((acc, qa) => {
          acc[qa.questionKey] = qa.score;
          return acc;
        }, {})
      },
      metadata: {
        submittedAt: submittedAt || new Date().toISOString(),
        totalQuestions: questionsAndAnswers.length
      }
    };
    
    // Insert questionnaire submission
    const insertQuery = `
      INSERT INTO thc_increase_questionnaire (
        patient_id,
        email,
        zoho_id,
        questionnaire_data,
        total_score,
        max_score,
        is_eligible,
        status,
        ip_address,
        user_agent
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, 'submitted', $8, $9)
      RETURNING id, total_score, is_eligible, status, created_at
    `;

    const insertResult = await client.query(insertQuery, [
      patientID,
      req.userEmail,
      zohoID,
      JSON.stringify(questionnaireData),
      totalScore,
      maxScore || 61,
      isEligible,
      ipAddress,
      userAgent
    ]);
    
    await client.query('COMMIT');
    
    const submission = insertResult.rows[0];
    
    res.json({
      success: true,
      message: "THC increase questionnaire submitted successfully",
      data: {
        id: submission.id,
        totalScore: submission.total_score,
        isEligible: submission.is_eligible,
        status: submission.status,
        submittedAt: submission.created_at
      }
    });
    
  } catch (error) {
    await client.query('ROLLBACK');
    console.error('Error submitting THC questionnaire:', error);
    
    res.status(500).json({
      success: false,
      error: "Failed to submit questionnaire",
      code: 500
    });
  } finally {
    client.release();
  }
});

// GET /funnel/v1.0/patient/thc-increase-questionnaire/status
router.get('/thc-increase-questionnaire/status', authenticateUser, async (req, res) => {
  try {
    const { email } = req.query;
    
    // Use email from query parameter or authenticated user
    const queryEmail = email || req.userEmail;
    
    // Security check: users can only access their own data
    if (email && email !== req.userEmail) {
      return res.status(403).json({
        success: false,
        error: "Access denied",
        code: 403
      });
    }
    
    const query = `
      SELECT 
        id,
        total_score,
        max_score,
        is_eligible,
        status,
        created_at as submitted_at,
        reviewed_at,
        approved_at,
        questionnaire_data
      FROM thc_increase_questionnaire 
      WHERE email = $1 
      ORDER BY created_at DESC 
      LIMIT 1
    `;
    
    const result = await pool.query(query, [queryEmail]);
    
    if (result.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: "No THC increase questionnaire found for this patient"
      });
    }
    
    const questionnaire = result.rows[0];
    
    res.json({
      success: true,
      questionnaire: {
        id: questionnaire.id,
        totalScore: questionnaire.total_score,
        maxScore: questionnaire.max_score,
        isEligible: questionnaire.is_eligible,
        status: questionnaire.status,
        submittedAt: questionnaire.submitted_at,
        reviewedAt: questionnaire.reviewed_at,
        approvedAt: questionnaire.approved_at
      }
    });
    
  } catch (error) {
    console.error('Error fetching THC questionnaire status:', error);
    
    res.status(500).json({
      success: false,
      error: "Failed to fetch questionnaire status",
      code: 500
    });
  }
});

module.exports = router;
