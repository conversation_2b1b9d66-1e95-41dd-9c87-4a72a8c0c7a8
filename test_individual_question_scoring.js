// Test individual question scoring for Add22Thc questionnaire
// This verifies that each question gets its proper score for analytics

console.log("=== Individual Question Scoring Test ===\n");

// Scoring mapping object - matches the component implementation
const scoringMap = {
  // Question 1: Reasons (checkbox values)
  reasonSideEffects: { true: 4, false: 0 },
  reasonGentlerEffect: { true: 3, false: 0 },
  reasonDifferentStrain: { true: 2, false: 0 },
  reasonTolerance: { true: 3, false: 0 },
  reasonOther: { true: 1, false: 0 },

  // Question 2: Symptom improvement (1-10 scale)
  symptomImprovement: {
    "1": 0, "2": 0, "3": 1, "4": 1, "5": 2,
    "6": 2, "7": 3, "8": 3, "9": 4, "10": 4
  },

  // Question 2: Side effects (checkbox values)
  sideEffectsNone: { true: 4, false: 0 },
  sideEffectsMild: { true: 2, false: 0 },
  sideEffectsModerate: { true: 1, false: 0 },
  sideEffectsStrong: { true: 0, false: 0 },

  // Question 3: Health changes
  healthChanges: {
    "no-changes": 3,
    "yes": 1
  },

  // Question 4: Usage plan
  usagePlan: {
    "alternative-situations": 4,
    "rotation-time-symptoms": 3,
    "unsure-advice": 2,
    "other": 1
  },

  // Question 5: Consent
  consent: {
    "yes": 5,
    "no": 0
  }
};

// Calculate score for individual question
function calculateQuestionScore(questionKey, answer) {
  const questionScoring = scoringMap[questionKey];
  if (!questionScoring) return 0;

  const answerKey = typeof answer === 'boolean' ? answer.toString() : answer;
  return questionScoring[answerKey] || 0;
}

// Simulate the questionsAndAnswers array creation with individual scoring
function createQuestionsAndAnswers(formData) {
  const questionsAndAnswers = [];

  // Question 1: Individual reason checkboxes
  questionsAndAnswers.push({
    questionKey: "reasonSideEffects",
    questionText: "Reason: Experiencing side effects with 29%",
    answerValue: formData.reasonSideEffects,
    answerText: formData.reasonSideEffects ? "Selected" : "Not selected",
    score: calculateQuestionScore('reasonSideEffects', formData.reasonSideEffects)
  });

  questionsAndAnswers.push({
    questionKey: "reasonGentlerEffect",
    questionText: "Reason: Prefer a gentler effect for daily use",
    answerValue: formData.reasonGentlerEffect,
    answerText: formData.reasonGentlerEffect ? "Selected" : "Not selected",
    score: calculateQuestionScore('reasonGentlerEffect', formData.reasonGentlerEffect)
  });

  questionsAndAnswers.push({
    questionKey: "reasonDifferentStrain",
    questionText: "Reason: Trying a different strain for symptom targeting",
    answerValue: formData.reasonDifferentStrain,
    answerText: formData.reasonDifferentStrain ? "Selected" : "Not selected",
    score: calculateQuestionScore('reasonDifferentStrain', formData.reasonDifferentStrain)
  });

  questionsAndAnswers.push({
    questionKey: "reasonTolerance",
    questionText: "Reason: Building tolerance to higher THC strain",
    answerValue: formData.reasonTolerance,
    answerText: formData.reasonTolerance ? "Selected" : "Not selected",
    score: calculateQuestionScore('reasonTolerance', formData.reasonTolerance)
  });

  questionsAndAnswers.push({
    questionKey: "reasonOther",
    questionText: "Reason: Other",
    answerValue: formData.reasonOther,
    answerText: formData.reasonOther ? `Selected: ${formData.reasonOtherText || 'No description'}` : "Not selected",
    score: calculateQuestionScore('reasonOther', formData.reasonOther)
  });

  // Question 2: Symptom improvement
  questionsAndAnswers.push({
    questionKey: "symptomImprovement",
    questionText: "How well has your current 29% THC treatment worked for you?",
    answerValue: formData.symptomImprovement,
    answerText: `${formData.symptomImprovement} - Rating`,
    score: calculateQuestionScore('symptomImprovement', formData.symptomImprovement)
  });

  // Question 3: Individual side effect checkboxes
  questionsAndAnswers.push({
    questionKey: "sideEffectsNone",
    questionText: "Side effects: None",
    answerValue: formData.sideEffectsNone,
    answerText: formData.sideEffectsNone ? "Selected" : "Not selected",
    score: calculateQuestionScore('sideEffectsNone', formData.sideEffectsNone)
  });

  questionsAndAnswers.push({
    questionKey: "sideEffectsMild",
    questionText: "Side effects: Mild",
    answerValue: formData.sideEffectsMild,
    answerText: formData.sideEffectsMild ? "Selected" : "Not selected",
    score: calculateQuestionScore('sideEffectsMild', formData.sideEffectsMild)
  });

  questionsAndAnswers.push({
    questionKey: "sideEffectsModerate",
    questionText: "Side effects: Moderate",
    answerValue: formData.sideEffectsModerate,
    answerText: formData.sideEffectsModerate ? "Selected" : "Not selected",
    score: calculateQuestionScore('sideEffectsModerate', formData.sideEffectsModerate)
  });

  questionsAndAnswers.push({
    questionKey: "sideEffectsStrong",
    questionText: "Side effects: Strong",
    answerValue: formData.sideEffectsStrong,
    answerText: formData.sideEffectsStrong ? "Selected" : "Not selected",
    score: calculateQuestionScore('sideEffectsStrong', formData.sideEffectsStrong)
  });

  // Question 4: Health changes
  questionsAndAnswers.push({
    questionKey: "healthChanges",
    questionText: "Have there been any changes in your health, medications, or lifestyle since your last consultation?",
    answerValue: formData.healthChanges,
    answerText: formData.healthChanges === 'yes' ? 'Yes, there have been changes' : 'No changes',
    score: calculateQuestionScore('healthChanges', formData.healthChanges)
  });

  // Question 5: Usage plan
  questionsAndAnswers.push({
    questionKey: "usagePlan",
    questionText: "How do you plan to use the 22% THC product alongside your 29%?",
    answerValue: formData.usagePlan,
    answerText: formData.usagePlan,
    score: calculateQuestionScore('usagePlan', formData.usagePlan)
  });

  // Question 6: Consent
  questionsAndAnswers.push({
    questionKey: "consent",
    questionText: "Do you consent to your doctor reviewing this information?",
    answerValue: formData.consent,
    answerText: formData.consent === 'yes' ? 'Yes' : 'No',
    score: calculateQuestionScore('consent', formData.consent)
  });

  return questionsAndAnswers;
}

// Test case: Patient with mixed responses
const testFormData = {
  // Reasons - some selected
  reasonSideEffects: true,      // Should score 4
  reasonGentlerEffect: false,   // Should score 0
  reasonDifferentStrain: true,  // Should score 2
  reasonTolerance: false,       // Should score 0
  reasonOther: true,            // Should score 1
  reasonOtherText: "Cost considerations",

  // Symptom improvement
  symptomImprovement: "8",      // Should score 3

  // Side effects - only mild selected
  sideEffectsNone: false,       // Should score 0
  sideEffectsMild: true,        // Should score 2
  sideEffectsModerate: false,   // Should score 0
  sideEffectsStrong: false,     // Should score 0

  // Health changes
  healthChanges: "no-changes",  // Should score 3

  // Usage plan
  usagePlan: "rotation-time-symptoms", // Should score 3

  // Consent
  consent: "yes"                // Should score 5
};

console.log("🧪 Testing individual question scoring...\n");

const questionsAndAnswers = createQuestionsAndAnswers(testFormData);

// Display results
let totalScore = 0;
questionsAndAnswers.forEach((qa, index) => {
  console.log(`${index + 1}. ${qa.questionKey}`);
  console.log(`   Question: ${qa.questionText}`);
  console.log(`   Answer: ${qa.answerText}`);
  console.log(`   Score: ${qa.score} points`);
  console.log("");
  
  totalScore += qa.score;
});

console.log("=== Summary ===");
console.log(`Total Score: ${totalScore} points`);
console.log(`Expected Total: 23 points (4+0+2+0+1+3+0+2+0+0+3+3+5)`);
console.log(`Match: ${totalScore === 23 ? '✅ CORRECT' : '❌ INCORRECT'}`);

// Verify no scores are 0 when they shouldn't be
console.log("\n=== Score Verification ===");
const expectedNonZeroScores = [
  'reasonSideEffects', 'reasonDifferentStrain', 'reasonOther', 
  'symptomImprovement', 'sideEffectsMild', 'healthChanges', 
  'usagePlan', 'consent'
];

let allCorrect = true;
expectedNonZeroScores.forEach(key => {
  const qa = questionsAndAnswers.find(q => q.questionKey === key);
  if (qa && qa.score > 0) {
    console.log(`✅ ${key}: ${qa.score} points (correct)`);
  } else {
    console.log(`❌ ${key}: ${qa?.score || 'not found'} points (should be > 0)`);
    allCorrect = false;
  }
});

console.log(`\n🎯 Individual scoring ${allCorrect ? 'WORKING CORRECTLY' : 'HAS ISSUES'}`);
console.log("Now backend can analyze which specific questions patients struggle with!");

// Show analytics potential
console.log("\n=== Analytics Potential ===");
console.log("With individual question scores, you can now analyze:");
console.log("• Which reasons are most commonly selected");
console.log("• Distribution of symptom improvement ratings");
console.log("• Most common side effect patterns");
console.log("• Health change frequency");
console.log("• Preferred usage plans");
console.log("• Consent rates");
console.log("• Which questions contribute most to failures");
